/**
 * Alert Manager for DumpDetector
 * Manages alert generation, filtering, and distribution
 */

import { Logger } from '../utils/Logger.js';

export class AlertManager {
  constructor({ database, redis, config }) {
    this.database = database;
    this.redis = redis;
    this.config = config;
    this.logger = new Logger('AlertManager');
    
    this.alertCooldowns = new Map();
    this.maxAlertsPerHour = config.get('alerts.maxAlertsPerHour') || 50;
  }

  async processRiskAssessment(tokenData, riskAssessment) {
    try {
      const shouldAlert = this.shouldGenerateAlert(tokenData, riskAssessment);
      
      if (!shouldAlert) {
        return null;
      }

      const alert = await this.generateAlert(tokenData, riskAssessment);
      
      // Save alert to database
      await this.database.saveAlert(alert);
      
      // Add to alert queue for processing
      await this.redis.addToAlertQueue(alert);
      
      this.logger.info(`🚨 Alert generated for ${tokenData.address}: ${alert.alertType}`);
      
      return alert;
    } catch (error) {
      this.logger.error('Error processing risk assessment:', error);
      return null;
    }
  }

  shouldGenerateAlert(tokenData, riskAssessment) {
    const { riskScore, riskLevel } = riskAssessment;
    
    // Check risk thresholds
    const thresholds = this.config.getRiskThresholds();
    if (riskScore < thresholds.medium) {
      return false;
    }

    // Check cooldown
    const cooldownKey = `${tokenData.address}:${riskLevel}`;
    const lastAlert = this.alertCooldowns.get(cooldownKey);
    const cooldownPeriod = 300000; // 5 minutes
    
    if (lastAlert && Date.now() - lastAlert < cooldownPeriod) {
      return false;
    }

    return true;
  }

  async generateAlert(tokenData, riskAssessment) {
    const alertType = this.determineAlertType(tokenData, riskAssessment);
    const message = this.generateAlertMessage(tokenData, riskAssessment, alertType);
    
    const alert = {
      tokenAddress: tokenData.address,
      tokenName: tokenData.name,
      tokenSymbol: tokenData.symbol,
      network: tokenData.network,
      
      alertType,
      riskLevel: riskAssessment.riskLevel,
      riskScore: riskAssessment.riskScore,
      
      message,
      
      details: {
        triggers: this.extractTriggers(riskAssessment),
        analysis: riskAssessment,
        recommendations: this.generateRecommendations(riskAssessment)
      },
      
      channels: {
        telegram: { sent: false },
        discord: { sent: false },
        webhook: { sent: false }
      },
      
      sent: false,
      createdAt: new Date()
    };

    // Update cooldown
    const cooldownKey = `${tokenData.address}:${riskAssessment.riskLevel}`;
    this.alertCooldowns.set(cooldownKey, Date.now());

    return alert;
  }

  determineAlertType(tokenData, riskAssessment) {
    const { riskScore, components } = riskAssessment;
    
    if (riskScore >= 0.9) return 'scam_detected';
    if (riskScore >= 0.8) return 'rugpull_risk';
    if (components.contract >= 0.8) return 'honeypot_detected';
    if (riskScore >= 0.6) return 'high_risk';
    return 'suspicious_activity';
  }

  generateAlertMessage(tokenData, riskAssessment, alertType) {
    const riskPercentage = (riskAssessment.riskScore * 100).toFixed(1);
    
    switch (alertType) {
      case 'scam_detected':
        return `SCAM DETECTED: ${tokenData.symbol} shows critical risk indicators (${riskPercentage}% risk score)`;
      case 'rugpull_risk':
        return `RUGPULL RISK: ${tokenData.symbol} exhibits high-risk patterns (${riskPercentage}% risk score)`;
      case 'honeypot_detected':
        return `HONEYPOT DETECTED: ${tokenData.symbol} may be a honeypot contract (${riskPercentage}% risk score)`;
      case 'high_risk':
        return `HIGH RISK TOKEN: ${tokenData.symbol} flagged for suspicious activity (${riskPercentage}% risk score)`;
      default:
        return `SUSPICIOUS ACTIVITY: ${tokenData.symbol} requires caution (${riskPercentage}% risk score)`;
    }
  }

  extractTriggers(riskAssessment) {
    const triggers = [];
    const { components } = riskAssessment;
    
    if (components.contract >= 0.7) triggers.push('High contract risk');
    if (components.social >= 0.7) triggers.push('Suspicious social activity');
    if (components.market >= 0.7) triggers.push('Unusual market behavior');
    if (components.liquidity >= 0.7) triggers.push('Low liquidity');
    
    return triggers;
  }

  generateRecommendations(riskAssessment) {
    const recommendations = [];
    const { riskLevel, riskScore } = riskAssessment;
    
    if (riskLevel === 'critical' || riskScore >= 0.8) {
      recommendations.push('AVOID this token completely');
      recommendations.push('Do not invest any funds');
      recommendations.push('Warn others about this token');
    } else if (riskLevel === 'high') {
      recommendations.push('Exercise extreme caution');
      recommendations.push('Conduct thorough research');
      recommendations.push('Consider the high risk before investing');
    } else {
      recommendations.push('Proceed with caution');
      recommendations.push('Do your own research (DYOR)');
      recommendations.push('Only invest what you can afford to lose');
    }
    
    return recommendations;
  }
}
