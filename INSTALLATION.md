# DumpDetector Installation Guide

## 🚀 Quick Start

### Prerequisites

- **Node.js 18+** - [Download here](https://nodejs.org/)
- **MongoDB** - [Installation guide](https://docs.mongodb.com/manual/installation/)
- **Redis** - [Installation guide](https://redis.io/download)
- **Git** - [Download here](https://git-scm.com/)

### 1. <PERSON><PERSON> and Install

```bash
# Clone the repository
git clone https://github.com/your-org/dump-detector.git
cd dump-detector

# Run installation script
node scripts/install.js

# Or install manually
npm install
cd frontend && npm install
```

### 2. Environment Configuration

Copy the example environment file and configure it:

```bash
cp .env.example .env
```

Edit `.env` with your configuration:

```env
# Required - Database
MONGODB_URI=mongodb://localhost:27017/dumpdetector
REDIS_URL=redis://localhost:6379

# Required - Telegram Bot
TELEGRAM_BOT_TOKEN=your_telegram_bot_token

# Required - At least one blockchain RPC
ETHEREUM_RPC_URL=https://mainnet.infura.io/v3/YOUR_INFURA_KEY
BSC_RPC_URL=https://bsc-dataseed.binance.org/
POLYGON_RPC_URL=https://polygon-rpc.com/

# Optional - API Keys for enhanced features
ETHERSCAN_API_KEY=your_etherscan_api_key
BSCSCAN_API_KEY=your_bscscan_api_key
TWITTER_BEARER_TOKEN=your_twitter_bearer_token
```

### 3. Database Setup

Start MongoDB and Redis:

```bash
# MongoDB (if installed locally)
mongod

# Redis (if installed locally)
redis-server
```

### 4. Telegram Bot Setup

1. Create a bot with [@BotFather](https://t.me/BotFather)
2. Get your bot token
3. Add the token to your `.env` file
4. Set webhook URL (optional for production)

### 5. Start the Application

```bash
# Development mode (with hot reload)
npm run dev

# Production mode
npm start
```

The application will be available at:
- **Backend API**: http://localhost:3000
- **Frontend Dashboard**: http://localhost:3001 (in development)

## 🐳 Docker Installation

### Using Docker Compose (Recommended)

```bash
# Clone repository
git clone https://github.com/your-org/dump-detector.git
cd dump-detector

# Configure environment
cp .env.example .env
# Edit .env with your settings

# Start all services
docker-compose up -d

# View logs
docker-compose logs -f dumpdetector
```

### Manual Docker Build

```bash
# Build image
docker build -t dump-detector .

# Run with external MongoDB and Redis
docker run -d \
  --name dump-detector \
  -p 3000:3000 \
  -e MONGODB_URI=mongodb://your-mongo-host:27017/dumpdetector \
  -e REDIS_URL=redis://your-redis-host:6379 \
  -e TELEGRAM_BOT_TOKEN=your_bot_token \
  dump-detector
```

## 🔧 Configuration

### Blockchain Networks

Configure which networks to monitor:

```env
# Ethereum
ETHEREUM_RPC_URL=https://mainnet.infura.io/v3/YOUR_KEY
ETHERSCAN_API_KEY=your_etherscan_key

# Binance Smart Chain
BSC_RPC_URL=https://bsc-dataseed.binance.org/
BSCSCAN_API_KEY=your_bscscan_key

# Polygon
POLYGON_RPC_URL=https://polygon-rpc.com/
```

### Risk Thresholds

Adjust risk detection sensitivity:

```env
HIGH_RISK_THRESHOLD=0.8
MEDIUM_RISK_THRESHOLD=0.6
LOW_RISK_THRESHOLD=0.4
```

### Scanning Configuration

Control scanning behavior:

```env
SCAN_INTERVAL_SECONDS=30
MAX_TOKENS_PER_SCAN=100
CONTRACT_ANALYSIS_TIMEOUT=10000
```

## 📱 Telegram Bot Commands

Once your bot is running, users can interact with it:

- `/start` - Initialize the bot
- `/subscribe` - Enable alert notifications
- `/unsubscribe` - Disable notifications
- `/analyze <address>` - Analyze a specific token
- `/settings` - Configure preferences
- `/status` - Check system status
- `/help` - Show help message

## 🔍 API Endpoints

### Public Endpoints

- `GET /api/health` - System health check
- `GET /api/tokens` - List monitored tokens
- `GET /api/tokens/:address` - Get specific token
- `POST /api/tokens/analyze` - Analyze token
- `GET /api/alerts` - List recent alerts
- `GET /api/stats` - System statistics

### WebSocket Events

Real-time events for frontend:

- `token_detected` - New token discovered
- `risk_alert` - High-risk token alert
- `analysis_complete` - Analysis finished
- `system_status` - System status update

## 🛠️ Development

### Project Structure

```
dump-detector/
├── src/                    # Backend source code
│   ├── collectors/         # Data collection modules
│   ├── analyzers/          # Analysis engines
│   ├── models/             # AI/ML models
│   ├── alerts/             # Alert system
│   ├── api/                # REST API
│   ├── database/           # Database layer
│   └── utils/              # Utilities
├── frontend/               # React dashboard
├── tests/                  # Test suites
├── scripts/                # Utility scripts
└── docs/                   # Documentation
```

### Running Tests

```bash
# Backend tests
npm test

# Frontend tests
cd frontend && npm test

# Run specific test suite
npm test -- --testPathPattern=analyzers
```

### Development Commands

```bash
# Start backend in development mode
npm run dev

# Start frontend development server
cd frontend && npm start

# Build frontend for production
cd frontend && npm run build

# Lint code
npm run lint

# Fix linting issues
npm run lint:fix
```

## 🚨 Troubleshooting

### Common Issues

**MongoDB Connection Failed**
```bash
# Check if MongoDB is running
mongod --version
systemctl status mongod  # Linux
brew services list | grep mongodb  # macOS
```

**Redis Connection Failed**
```bash
# Check if Redis is running
redis-cli ping
systemctl status redis  # Linux
brew services list | grep redis  # macOS
```

**Telegram Bot Not Responding**
- Verify bot token in `.env`
- Check bot permissions with @BotFather
- Ensure webhook URL is accessible (for production)

**No Tokens Being Detected**
- Verify blockchain RPC URLs are working
- Check API rate limits
- Review logs for errors: `tail -f logs/app.log`

### Performance Optimization

**High Memory Usage**
- Reduce `MAX_TOKENS_PER_SCAN`
- Increase `SCAN_INTERVAL_SECONDS`
- Enable Redis memory optimization

**Slow Analysis**
- Reduce `CONTRACT_ANALYSIS_TIMEOUT`
- Use faster RPC providers
- Enable analysis caching

## 📊 Monitoring

### Health Checks

```bash
# Check system health
curl http://localhost:3000/api/health

# Check specific services
curl http://localhost:3000/api/stats/system
```

### Logs

```bash
# View application logs
tail -f logs/app.log

# View error logs
tail -f logs/error.log

# Docker logs
docker-compose logs -f dumpdetector
```

## 🔒 Security

### Production Deployment

1. **Use HTTPS** - Configure SSL certificates
2. **Secure Database** - Enable authentication
3. **Rate Limiting** - Configure appropriate limits
4. **API Keys** - Secure storage and rotation
5. **Firewall** - Restrict access to necessary ports

### Environment Variables

Never commit sensitive data to version control:

```bash
# Add to .gitignore
.env
.env.local
.env.production
```

## 🆘 Support

- **Documentation**: [README.md](README.md)
- **Issues**: [GitHub Issues](https://github.com/your-org/dump-detector/issues)
- **Telegram**: [@DumpDetectorSupport](https://t.me/DumpDetectorSupport)
- **Email**: <EMAIL>

## 📄 License

This project is licensed under the MIT License - see [LICENSE](LICENSE) file for details.
