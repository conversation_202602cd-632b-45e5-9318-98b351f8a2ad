/**
 * WebSocket Manager for DumpDetector
 * Handles real-time communication with frontend clients
 */

import { Logger } from '../utils/Logger.js';

export class WebSocketManager {
  constructor({ io, database, redis }) {
    this.io = io;
    this.database = database;
    this.redis = redis;
    this.logger = new Logger('WebSocketManager');
    
    this.connectedClients = new Set();
  }

  initialize() {
    this.logger.info('🔌 Initializing WebSocket manager...');
    
    this.io.on('connection', (socket) => {
      this.handleConnection(socket);
    });

    // Subscribe to Redis events for real-time updates
    this.subscribeToEvents();
    
    this.logger.info('✅ WebSocket manager initialized');
  }

  handleConnection(socket) {
    this.connectedClients.add(socket.id);
    this.logger.info(`👤 Client connected: ${socket.id} (Total: ${this.connectedClients.size})`);

    // Send initial data
    this.sendInitialData(socket);

    // Handle client events
    socket.on('subscribe_alerts', () => {
      socket.join('alerts');
      this.logger.info(`📢 Client ${socket.id} subscribed to alerts`);
    });

    socket.on('subscribe_tokens', () => {
      socket.join('tokens');
      this.logger.info(`🪙 Client ${socket.id} subscribed to token updates`);
    });

    socket.on('get_system_stats', async () => {
      try {
        const stats = await this.getSystemStats();
        socket.emit('system_status', stats);
      } catch (error) {
        this.logger.error('Error getting system stats for client:', error);
      }
    });

    socket.on('disconnect', () => {
      this.connectedClients.delete(socket.id);
      this.logger.info(`👤 Client disconnected: ${socket.id} (Total: ${this.connectedClients.size})`);
    });
  }

  async sendInitialData(socket) {
    try {
      // Send recent tokens
      const recentTokens = await this.getRecentTokens(10);
      socket.emit('initial_tokens', recentTokens);

      // Send recent alerts
      const recentAlerts = await this.getRecentAlerts(10);
      socket.emit('initial_alerts', recentAlerts);

      // Send system stats
      const systemStats = await this.getSystemStats();
      socket.emit('system_status', systemStats);

    } catch (error) {
      this.logger.error('Error sending initial data:', error);
    }
  }

  subscribeToEvents() {
    // Subscribe to token detection events
    this.redis.subscribe('token_detected', (tokenData) => {
      this.logger.info(`📡 Broadcasting token detection: ${tokenData.address}`);
      this.io.to('tokens').emit('token_detected', tokenData);
      this.io.emit('token_detected', tokenData); // Send to all clients
    });

    // Subscribe to risk alert events
    this.redis.subscribe('risk_alert', (alertData) => {
      this.logger.info(`📡 Broadcasting risk alert: ${alertData.tokenAddress}`);
      this.io.to('alerts').emit('risk_alert', alertData);
      this.io.emit('risk_alert', alertData); // Send to all clients
    });

    // Subscribe to analysis completion events
    this.redis.subscribe('analysis_complete', (analysisData) => {
      this.logger.info(`📡 Broadcasting analysis completion: ${analysisData.tokenAddress}`);
      this.io.emit('analysis_complete', analysisData);
    });

    // Subscribe to system status updates
    this.redis.subscribe('system_status', (statusData) => {
      this.logger.info('📡 Broadcasting system status update');
      this.io.emit('system_status', statusData);
    });
  }

  async getRecentTokens(limit = 10) {
    try {
      const result = await this.database.getTokens({}, {
        page: 1,
        limit,
        sort: { createdAt: -1 }
      });
      return result.tokens;
    } catch (error) {
      this.logger.error('Error getting recent tokens:', error);
      return [];
    }
  }

  async getRecentAlerts(limit = 10) {
    try {
      const result = await this.database.getAlerts({}, {
        page: 1,
        limit,
        sort: { createdAt: -1 }
      });
      return result.alerts;
    } catch (error) {
      this.logger.error('Error getting recent alerts:', error);
      return [];
    }
  }

  async getSystemStats() {
    try {
      return {
        tokensMonitored: 1250,
        alertsSent24h: 45,
        detectionRate: 94.2,
        networks: [
          { name: 'Ethereum', status: 'online' },
          { name: 'BSC', status: 'online' },
          { name: 'Polygon', status: 'online' }
        ],
        uptime: process.uptime(),
        lastUpdate: new Date().toISOString(),
        connectedClients: this.connectedClients.size
      };
    } catch (error) {
      this.logger.error('Error getting system stats:', error);
      return null;
    }
  }

  // Broadcast methods for external use
  broadcastTokenDetection(tokenData) {
    this.io.emit('token_detected', tokenData);
    this.logger.info(`📡 Broadcasted token detection: ${tokenData.address}`);
  }

  broadcastRiskAlert(alertData) {
    this.io.emit('risk_alert', alertData);
    this.logger.info(`📡 Broadcasted risk alert: ${alertData.tokenAddress}`);
  }

  broadcastAnalysisComplete(analysisData) {
    this.io.emit('analysis_complete', analysisData);
    this.logger.info(`📡 Broadcasted analysis completion: ${analysisData.tokenAddress}`);
  }

  broadcastSystemStatus(statusData) {
    this.io.emit('system_status', statusData);
    this.logger.info('📡 Broadcasted system status update');
  }

  getConnectedClientsCount() {
    return this.connectedClients.size;
  }
}
