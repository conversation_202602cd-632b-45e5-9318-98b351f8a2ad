import React from 'react';
import {
  <PERSON>,
  Typography,
  Card,
  CardContent,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Tooltip,
  Alert as Mui<PERSON>lert,
} from '@mui/material';
import {
  Security,
  Warning,
  Error,
  Visibility,
  OpenInNew,
  NotificationsActive,
} from '@mui/icons-material';
import { useSocket } from '../contexts/SocketContext';
import { format } from 'date-fns';

const Alerts: React.FC = () => {
  const { latestAlerts } = useSocket();

  const getRiskColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'low': return '#00ff88';
      case 'medium': return '#ffaa00';
      case 'high': return '#ff4444';
      case 'critical': return '#ff0000';
      default: return '#cccccc';
    }
  };

  const getRiskIcon = (riskLevel: string) => {
    switch (riskLevel) {
      case 'low': return <Security sx={{ color: '#00ff88' }} />;
      case 'medium': return <Warning sx={{ color: '#ffaa00' }} />;
      case 'high': return <Error sx={{ color: '#ff4444' }} />;
      case 'critical': return <Error sx={{ color: '#ff0000' }} />;
      default: return <Security sx={{ color: '#cccccc' }} />;
    }
  };

  const getAlertTypeColor = (alertType: string) => {
    switch (alertType) {
      case 'scam_detected': return 'error';
      case 'rugpull_risk': return 'error';
      case 'honeypot_detected': return 'error';
      case 'high_risk': return 'warning';
      case 'suspicious_activity': return 'warning';
      default: return 'default';
    }
  };

  return (
    <Box sx={{ flexGrow: 1 }}>
      <Typography variant="h4" sx={{ mb: 3, fontWeight: 700 }}>
        🚨 Security Alerts
      </Typography>

      {/* Alert Summary */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <NotificationsActive sx={{ color: '#ff4444', mr: 1 }} />
            <Typography variant="h6">
              Alert Summary
            </Typography>
          </Box>
          
          <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
            <Chip
              label={`Total Alerts: ${latestAlerts.length}`}
              color="primary"
              variant="outlined"
            />
            <Chip
              label={`Critical: ${latestAlerts.filter(a => a.riskLevel === 'critical').length}`}
              sx={{ backgroundColor: '#ff0000', color: '#ffffff' }}
            />
            <Chip
              label={`High: ${latestAlerts.filter(a => a.riskLevel === 'high').length}`}
              sx={{ backgroundColor: '#ff4444', color: '#ffffff' }}
            />
            <Chip
              label={`Medium: ${latestAlerts.filter(a => a.riskLevel === 'medium').length}`}
              sx={{ backgroundColor: '#ffaa00', color: '#000000' }}
            />
          </Box>
        </CardContent>
      </Card>

      {/* Recent Alerts Notice */}
      {latestAlerts.length > 0 && (
        <MuiAlert severity="warning" sx={{ mb: 3 }}>
          <strong>Live Alert Feed:</strong> Showing the most recent security alerts detected by DumpDetector. 
          Always verify information independently before making investment decisions.
        </MuiAlert>
      )}

      {/* Alerts Table */}
      <Card>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2 }}>
            Recent Security Alerts
          </Typography>
          
          {latestAlerts.length === 0 ? (
            <Box sx={{ textAlign: 'center', py: 4 }}>
              <Security sx={{ fontSize: 64, color: '#00ff88', mb: 2 }} />
              <Typography variant="h6" sx={{ color: '#00ff88' }}>
                No Recent Alerts
              </Typography>
              <Typography variant="body2" sx={{ color: '#cccccc' }}>
                All monitored tokens appear safe. We'll notify you immediately if any threats are detected.
              </Typography>
            </Box>
          ) : (
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Alert Type</TableCell>
                    <TableCell>Token</TableCell>
                    <TableCell>Network</TableCell>
                    <TableCell>Risk Level</TableCell>
                    <TableCell>Risk Score</TableCell>
                    <TableCell>Time</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {latestAlerts.map((alert) => (
                    <TableRow 
                      key={alert._id}
                      sx={{
                        borderLeft: `4px solid ${getRiskColor(alert.riskLevel)}`,
                        '&:hover': {
                          backgroundColor: 'rgba(255, 255, 255, 0.02)'
                        }
                      }}
                    >
                      <TableCell>
                        <Chip
                          label={alert.alertType.replace('_', ' ').toUpperCase()}
                          color={getAlertTypeColor(alert.alertType) as any}
                          size="small"
                          sx={{ fontWeight: 600 }}
                        />
                      </TableCell>
                      
                      <TableCell>
                        <Box>
                          <Typography variant="body2" sx={{ fontWeight: 600 }}>
                            {alert.tokenName} ({alert.tokenSymbol})
                          </Typography>
                          <Typography variant="caption" sx={{ color: '#cccccc' }}>
                            {alert.tokenAddress.slice(0, 10)}...{alert.tokenAddress.slice(-8)}
                          </Typography>
                        </Box>
                      </TableCell>
                      
                      <TableCell>
                        <Chip 
                          label={alert.network.toUpperCase()} 
                          size="small" 
                          variant="outlined"
                        />
                      </TableCell>
                      
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          {getRiskIcon(alert.riskLevel)}
                          <Typography 
                            variant="body2" 
                            sx={{ 
                              ml: 1, 
                              color: getRiskColor(alert.riskLevel),
                              fontWeight: 600,
                              textTransform: 'uppercase'
                            }}
                          >
                            {alert.riskLevel}
                          </Typography>
                        </Box>
                      </TableCell>
                      
                      <TableCell>
                        <Typography 
                          variant="body2" 
                          sx={{ 
                            color: getRiskColor(alert.riskLevel),
                            fontWeight: 600
                          }}
                        >
                          {(alert.riskScore * 100).toFixed(1)}%
                        </Typography>
                      </TableCell>
                      
                      <TableCell>
                        <Typography variant="body2" sx={{ color: '#cccccc' }}>
                          {format(new Date(alert.createdAt), 'MMM dd, HH:mm:ss')}
                        </Typography>
                      </TableCell>
                      
                      <TableCell>
                        <Tooltip title="View Details">
                          <IconButton size="small" sx={{ color: '#00ff88' }}>
                            <Visibility />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="View on Explorer">
                          <IconButton size="small" sx={{ color: '#cccccc' }}>
                            <OpenInNew />
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </CardContent>
      </Card>

      {/* Alert Details */}
      {latestAlerts.length > 0 && (
        <Card sx={{ mt: 3 }}>
          <CardContent>
            <Typography variant="h6" sx={{ mb: 2 }}>
              Latest Alert Details
            </Typography>
            
            {latestAlerts.slice(0, 3).map((alert) => (
              <Box 
                key={alert._id}
                sx={{ 
                  p: 2, 
                  mb: 2, 
                  border: '1px solid #333', 
                  borderRadius: 1,
                  borderLeft: `4px solid ${getRiskColor(alert.riskLevel)}`
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  {getRiskIcon(alert.riskLevel)}
                  <Typography variant="body1" sx={{ ml: 1, fontWeight: 600 }}>
                    {alert.alertType.replace('_', ' ').toUpperCase()}
                  </Typography>
                  <Chip
                    label={alert.riskLevel.toUpperCase()}
                    size="small"
                    sx={{
                      ml: 2,
                      backgroundColor: getRiskColor(alert.riskLevel),
                      color: alert.riskLevel === 'medium' ? '#000000' : '#ffffff',
                      fontWeight: 600
                    }}
                  />
                </Box>
                
                <Typography variant="body2" sx={{ color: '#cccccc', mb: 1 }}>
                  <strong>Token:</strong> {alert.tokenName} ({alert.tokenSymbol}) on {alert.network.toUpperCase()}
                </Typography>
                
                <Typography variant="body2" sx={{ color: '#cccccc', mb: 1 }}>
                  <strong>Message:</strong> {alert.message}
                </Typography>
                
                {alert.details.triggers.length > 0 && (
                  <Box sx={{ mt: 1 }}>
                    <Typography variant="body2" sx={{ color: '#cccccc', mb: 1 }}>
                      <strong>Triggers:</strong>
                    </Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                      {alert.details.triggers.map((trigger, index) => (
                        <Chip
                          key={index}
                          label={trigger}
                          size="small"
                          variant="outlined"
                          sx={{ color: '#ff4444', borderColor: '#ff4444' }}
                        />
                      ))}
                    </Box>
                  </Box>
                )}
                
                <Typography variant="caption" sx={{ color: '#888', mt: 1, display: 'block' }}>
                  Detected: {format(new Date(alert.createdAt), 'MMM dd, yyyy HH:mm:ss')} | 
                  Risk Score: {(alert.riskScore * 100).toFixed(1)}%
                </Typography>
              </Box>
            ))}
          </CardContent>
        </Card>
      )}
    </Box>
  );
};

export default Alerts;
