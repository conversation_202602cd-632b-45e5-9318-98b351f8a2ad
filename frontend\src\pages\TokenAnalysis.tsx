import React, { useState } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  TextField,
  Button,
  Grid,
  Chip,
  LinearProgress,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material';
import { Search, Security, Warning, Error } from '@mui/icons-material';

const TokenAnalysis: React.FC = () => {
  const [tokenAddress, setTokenAddress] = useState('');
  const [analyzing, setAnalyzing] = useState(false);
  const [analysisResult, setAnalysisResult] = useState<any>(null);

  const handleAnalyze = async () => {
    if (!tokenAddress) return;
    
    setAnalyzing(true);
    
    // Simulate analysis
    setTimeout(() => {
      setAnalysisResult({
        address: tokenAddress,
        name: 'Example Token',
        symbol: 'EXT',
        network: 'ethereum',
        riskScore: 0.75,
        riskLevel: 'high',
        riskFactors: [
          { factor: 'High owner concentration', weight: 0.3, score: 0.8, description: 'Owner holds 65% of total supply' },
          { factor: 'Unverified contract', weight: 0.2, score: 0.7, description: 'Contract source code is not verified' },
          { factor: 'Low liquidity', weight: 0.25, score: 0.9, description: 'Total liquidity is only $5,000' },
          { factor: 'New contract', weight: 0.15, score: 0.6, description: 'Contract created less than 24 hours ago' },
        ],
        analysis: {
          isScam: false,
          isRugpull: true,
          isHoneypot: false,
          hasHiddenMint: true,
          hasBlacklist: false,
          hasPausable: true,
        }
      });
      setAnalyzing(false);
    }, 3000);
  };

  const getRiskColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'low': return '#00ff88';
      case 'medium': return '#ffaa00';
      case 'high': return '#ff4444';
      case 'critical': return '#ff0000';
      default: return '#cccccc';
    }
  };

  const getRiskIcon = (riskLevel: string) => {
    switch (riskLevel) {
      case 'low': return <Security sx={{ color: '#00ff88' }} />;
      case 'medium': return <Warning sx={{ color: '#ffaa00' }} />;
      case 'high': return <Error sx={{ color: '#ff4444' }} />;
      case 'critical': return <Error sx={{ color: '#ff0000' }} />;
      default: return <Security sx={{ color: '#cccccc' }} />;
    }
  };

  return (
    <Box sx={{ flexGrow: 1 }}>
      <Typography variant="h4" sx={{ mb: 3, fontWeight: 700 }}>
        🔍 Token Analysis
      </Typography>

      {/* Analysis Input */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2 }}>
            Analyze Token Contract
          </Typography>
          <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
            <TextField
              fullWidth
              label="Token Contract Address"
              placeholder="0x..."
              value={tokenAddress}
              onChange={(e) => setTokenAddress(e.target.value)}
              disabled={analyzing}
            />
            <Button
              variant="contained"
              startIcon={<Search />}
              onClick={handleAnalyze}
              disabled={!tokenAddress || analyzing}
              sx={{ minWidth: 120 }}
            >
              {analyzing ? 'Analyzing...' : 'Analyze'}
            </Button>
          </Box>
          {analyzing && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="body2" sx={{ mb: 1, color: '#cccccc' }}>
                Analyzing contract... This may take a few moments.
              </Typography>
              <LinearProgress />
            </Box>
          )}
        </CardContent>
      </Card>

      {/* Analysis Results */}
      {analysisResult && (
        <Grid container spacing={3}>
          {/* Overview */}
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  Risk Assessment
                </Typography>
                
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  {getRiskIcon(analysisResult.riskLevel)}
                  <Typography 
                    variant="h4" 
                    sx={{ 
                      ml: 1, 
                      color: getRiskColor(analysisResult.riskLevel),
                      fontWeight: 700
                    }}
                  >
                    {(analysisResult.riskScore * 100).toFixed(1)}%
                  </Typography>
                </Box>

                <Chip
                  label={`${analysisResult.riskLevel.toUpperCase()} RISK`}
                  sx={{
                    backgroundColor: getRiskColor(analysisResult.riskLevel),
                    color: '#000000',
                    fontWeight: 700,
                    mb: 2
                  }}
                />

                <Typography variant="body2" sx={{ color: '#cccccc' }}>
                  {analysisResult.name} ({analysisResult.symbol})
                </Typography>
                <Typography variant="caption" sx={{ color: '#888' }}>
                  {analysisResult.address}
                </Typography>
              </CardContent>
            </Card>

            {/* Security Flags */}
            <Card sx={{ mt: 2 }}>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  Security Flags
                </Typography>
                
                {Object.entries(analysisResult.analysis).map(([key, value]) => (
                  <Box key={key} sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body2" sx={{ color: '#cccccc' }}>
                      {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                    </Typography>
                    <Chip
                      label={value ? 'Yes' : 'No'}
                      size="small"
                      color={value ? 'error' : 'success'}
                      variant="outlined"
                    />
                  </Box>
                ))}
              </CardContent>
            </Card>
          </Grid>

          {/* Risk Factors */}
          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  Risk Factors Analysis
                </Typography>

                {analysisResult.riskLevel === 'high' && (
                  <Alert severity="error" sx={{ mb: 2 }}>
                    ⚠️ HIGH RISK TOKEN DETECTED - Exercise extreme caution!
                  </Alert>
                )}

                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Risk Factor</TableCell>
                        <TableCell>Weight</TableCell>
                        <TableCell>Score</TableCell>
                        <TableCell>Description</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {analysisResult.riskFactors.map((factor: any, index: number) => (
                        <TableRow key={index}>
                          <TableCell>
                            <Typography variant="body2" sx={{ fontWeight: 600 }}>
                              {factor.factor}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {(factor.weight * 100).toFixed(0)}%
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <LinearProgress
                                variant="determinate"
                                value={factor.score * 100}
                                sx={{
                                  width: 60,
                                  mr: 1,
                                  '& .MuiLinearProgress-bar': {
                                    backgroundColor: factor.score > 0.7 ? '#ff4444' : 
                                                   factor.score > 0.4 ? '#ffaa00' : '#00ff88'
                                  }
                                }}
                              />
                              <Typography variant="body2">
                                {(factor.score * 100).toFixed(0)}%
                              </Typography>
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2" sx={{ color: '#cccccc' }}>
                              {factor.description}
                            </Typography>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>

                <Box sx={{ mt: 3, p: 2, backgroundColor: '#1a1a1a', borderRadius: 1 }}>
                  <Typography variant="h6" sx={{ mb: 1 }}>
                    Recommendation
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#cccccc' }}>
                    {analysisResult.riskLevel === 'high' || analysisResult.riskLevel === 'critical' 
                      ? '🚨 HIGH RISK - Avoid this token! Multiple red flags detected.'
                      : analysisResult.riskLevel === 'medium'
                      ? '⚠️ MEDIUM RISK - Proceed with caution and do your own research.'
                      : '✅ LOW RISK - Token appears relatively safe, but always DYOR.'
                    }
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}
    </Box>
  );
};

export default TokenAnalysis;
