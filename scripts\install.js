#!/usr/bin/env node

/**
 * DumpDetector Installation Script
 * Sets up the development environment and dependencies
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

console.log('🚀 DumpDetector Installation Script');
console.log('=====================================\n');

// Check Node.js version
const nodeVersion = process.version;
const requiredVersion = 'v18.0.0';

console.log(`📋 Checking Node.js version: ${nodeVersion}`);
if (nodeVersion < requiredVersion) {
  console.error(`❌ Node.js ${requiredVersion} or higher is required`);
  process.exit(1);
}
console.log('✅ Node.js version check passed\n');

// Install backend dependencies
console.log('📦 Installing backend dependencies...');
try {
  execSync('npm install', { stdio: 'inherit' });
  console.log('✅ Backend dependencies installed\n');
} catch (error) {
  console.error('❌ Failed to install backend dependencies');
  process.exit(1);
}

// Install frontend dependencies
console.log('📦 Installing frontend dependencies...');
try {
  execSync('cd frontend && npm install', { stdio: 'inherit', shell: true });
  console.log('✅ Frontend dependencies installed\n');
} catch (error) {
  console.error('❌ Failed to install frontend dependencies');
  process.exit(1);
}

// Create logs directory
console.log('📁 Creating logs directory...');
const logsDir = path.join(process.cwd(), 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
  console.log('✅ Logs directory created');
} else {
  console.log('✅ Logs directory already exists');
}

// Check for .env file
console.log('\n🔧 Checking environment configuration...');
const envFile = path.join(process.cwd(), '.env');
if (!fs.existsSync(envFile)) {
  console.log('📝 Creating .env file from template...');
  const envExample = path.join(process.cwd(), '.env.example');
  if (fs.existsSync(envExample)) {
    fs.copyFileSync(envExample, envFile);
    console.log('✅ .env file created from .env.example');
    console.log('⚠️  Please edit .env file with your configuration');
  } else {
    console.log('❌ .env.example not found');
  }
} else {
  console.log('✅ .env file already exists');
}

console.log('\n🎉 Installation completed successfully!');
console.log('\n📋 Next steps:');
console.log('1. Edit .env file with your configuration');
console.log('2. Set up MongoDB and Redis');
console.log('3. Configure API keys for blockchain providers');
console.log('4. Set up Telegram bot token');
console.log('5. Run: npm run dev');
console.log('\n📚 Documentation: README.md');
console.log('🆘 Support: https://github.com/your-org/dump-detector/issues');
