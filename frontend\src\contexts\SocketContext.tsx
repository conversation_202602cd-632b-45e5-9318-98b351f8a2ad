import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { io, Socket } from 'socket.io-client';
import { Token, Alert, Analysis, SystemStats, SocketEvents } from '../types';

interface SocketContextType {
  socket: Socket | null;
  connected: boolean;
  latestTokens: Token[];
  latestAlerts: Alert[];
  systemStats: SystemStats | null;
}

const SocketContext = createContext<SocketContextType>({
  socket: null,
  connected: false,
  latestTokens: [],
  latestAlerts: [],
  systemStats: null,
});

export const useSocket = () => {
  const context = useContext(SocketContext);
  if (!context) {
    throw new Error('useSocket must be used within a SocketProvider');
  }
  return context;
};

interface SocketProviderProps {
  children: ReactNode;
}

export const SocketProvider: React.FC<SocketProviderProps> = ({ children }) => {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [connected, setConnected] = useState(false);
  const [latestTokens, setLatestTokens] = useState<Token[]>([]);
  const [latestAlerts, setLatestAlerts] = useState<Alert[]>([]);
  const [systemStats, setSystemStats] = useState<SystemStats | null>(null);

  useEffect(() => {
    // Initialize socket connection
    const socketInstance = io(process.env.REACT_APP_API_URL || 'http://localhost:3000', {
      transports: ['websocket'],
      autoConnect: true,
    });

    setSocket(socketInstance);

    // Connection event handlers
    socketInstance.on('connect', () => {
      console.log('🔌 Connected to DumpDetector server');
      setConnected(true);
    });

    socketInstance.on('disconnect', () => {
      console.log('🔌 Disconnected from DumpDetector server');
      setConnected(false);
    });

    socketInstance.on('connect_error', (error) => {
      console.error('❌ Socket connection error:', error);
      setConnected(false);
    });

    // Token detection events
    socketInstance.on('token_detected', (token: Token) => {
      console.log('🪙 New token detected:', token.address);
      setLatestTokens(prev => [token, ...prev.slice(0, 49)]); // Keep last 50 tokens
    });

    // Risk alert events
    socketInstance.on('risk_alert', (alert: Alert) => {
      console.log('🚨 Risk alert:', alert.tokenAddress);
      setLatestAlerts(prev => [alert, ...prev.slice(0, 99)]); // Keep last 100 alerts
      
      // Show browser notification if supported
      if ('Notification' in window && Notification.permission === 'granted') {
        new Notification(`🚨 ${alert.alertType.toUpperCase()}`, {
          body: `${alert.tokenName} (${alert.tokenSymbol}) - Risk: ${alert.riskLevel}`,
          icon: '/favicon.ico',
          tag: alert.tokenAddress,
        });
      }
    });

    // Analysis completion events
    socketInstance.on('analysis_complete', (analysis: Analysis) => {
      console.log('✅ Analysis complete:', analysis.tokenAddress);
    });

    // System status updates
    socketInstance.on('system_status', (stats: SystemStats) => {
      setSystemStats(stats);
    });

    // Cleanup on unmount
    return () => {
      socketInstance.disconnect();
    };
  }, []);

  // Request notification permission on mount
  useEffect(() => {
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission().then(permission => {
        console.log('Notification permission:', permission);
      });
    }
  }, []);

  const value: SocketContextType = {
    socket,
    connected,
    latestTokens,
    latestAlerts,
    systemStats,
  };

  return (
    <SocketContext.Provider value={value}>
      {children}
    </SocketContext.Provider>
  );
};
