#!/usr/bin/env node

/**
 * Simple AI Test
 * Test different models and prompts
 */

import https from 'https';

const OPENROUTER_API_KEY = '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';

function makePostRequest(url, data, headers = {}) {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify(data);
    const urlObj = new URL(url);
    
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port || 443,
      path: urlObj.pathname,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': postData.length,
        ...headers
      }
    };

    const req = https.request(options, (res) => {
      let responseData = '';
      res.on('data', (chunk) => responseData += chunk);
      res.on('end', () => {
        try {
          resolve(JSON.parse(responseData));
        } catch (error) {
          reject(new Error(`Failed to parse response: ${error.message}`));
        }
      });
    });

    req.on('error', reject);
    req.setTimeout(60000, () => {
      req.destroy();
      reject(new Error('AI request timeout'));
    });

    req.write(postData);
    req.end();
  });
}

async function testSimpleAI() {
  try {
    console.log('🤖 Testing Simple AI Request...');
    
    const simplePrompt = `Analyze the cryptocurrency token USDT (Tether USD) at address ******************************************.

Provide:
1. Security Score (0-100)
2. Risk Level (Low/Medium/High/Critical)
3. Key risks
4. Investment recommendation

Keep response under 500 words.`;

    const response = await makePostRequest(
      'https://openrouter.ai/api/v1/chat/completions',
      {
        model: 'z-ai/glm-4.5-air:free',
        messages: [
          {
            role: 'user',
            content: simplePrompt
          }
        ],
        max_tokens: 800,
        temperature: 0.3
      },
      {
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'HTTP-Referer': 'https://dumpdetector.com',
        'X-Title': 'DumpDetector Simple Test'
      }
    );

    console.log('✅ AI Response Received');
    console.log('📊 Response:', JSON.stringify(response, null, 2));
    
    if (response.choices && response.choices[0] && response.choices[0].message) {
      console.log('\n📝 AI Analysis:');
      console.log(response.choices[0].message.content);
    }

  } catch (error) {
    console.error('❌ AI Test Failed:', error.message);
    console.error('Full error:', error);
  }
}

async function testDifferentModel() {
  try {
    console.log('\n🤖 Testing Different Model...');
    
    const response = await makePostRequest(
      'https://openrouter.ai/api/v1/chat/completions',
      {
        model: 'meta-llama/llama-3.2-3b-instruct:free',
        messages: [
          {
            role: 'user',
            content: 'Analyze USDT cryptocurrency. Give security score 0-100 and risk level.'
          }
        ],
        max_tokens: 500,
        temperature: 0.3
      },
      {
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'HTTP-Referer': 'https://dumpdetector.com',
        'X-Title': 'DumpDetector Model Test'
      }
    );

    console.log('✅ Alternative Model Response:');
    console.log('📊 Response:', JSON.stringify(response, null, 2));

  } catch (error) {
    console.error('❌ Alternative Model Test Failed:', error.message);
  }
}

async function runSimpleTests() {
  console.log('🧪 Starting Simple AI Tests...\n');
  
  await testSimpleAI();
  await testDifferentModel();
}

runSimpleTests().catch(console.error);
