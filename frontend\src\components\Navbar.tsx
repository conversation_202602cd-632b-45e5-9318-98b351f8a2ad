import React from 'react';
import {
  AppB<PERSON>,
  Tool<PERSON>,
  Typography,
  Button,
  Box,
  Chip,
  IconButton,
  Badge,
} from '@mui/material';
import {
  Security,
  Dashboard,
  Analytics,
  Notifications,
  Settings,
  Circle,
} from '@mui/icons-material';
import { useNavigate, useLocation } from 'react-router-dom';
import { useSocket } from '../contexts/SocketContext';

const Navbar: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { connected, latestAlerts, systemStats } = useSocket();

  const navItems = [
    { path: '/', label: 'Dashboard', icon: <Dashboard /> },
    { path: '/analysis', label: 'Analysis', icon: <Analytics /> },
    { path: '/alerts', label: 'Alerts', icon: <Notifications /> },
    { path: '/settings', label: 'Settings', icon: <Settings /> },
  ];

  const unreadAlerts = latestAlerts.filter(alert => 
    Date.now() - new Date(alert.createdAt).getTime() < 3600000 // Last hour
  ).length;

  return (
    <AppBar position="static" sx={{ backgroundColor: '#1a1a1a', borderBottom: '1px solid #333' }}>
      <Toolbar>
        {/* Logo and Title */}
        <Box sx={{ display: 'flex', alignItems: 'center', mr: 4 }}>
          <Security sx={{ mr: 1, color: '#00ff88' }} />
          <Typography variant="h6" component="div" sx={{ fontWeight: 700 }}>
            DumpDetector
          </Typography>
        </Box>

        {/* Connection Status */}
        <Chip
          icon={<Circle sx={{ fontSize: '12px !important' }} />}
          label={connected ? 'Online' : 'Offline'}
          size="small"
          color={connected ? 'success' : 'error'}
          sx={{ mr: 2 }}
        />

        {/* System Stats */}
        {systemStats && (
          <Box sx={{ display: 'flex', gap: 2, mr: 4 }}>
            <Typography variant="body2" sx={{ color: '#cccccc' }}>
              Tokens: {systemStats.tokensMonitored.toLocaleString()}
            </Typography>
            <Typography variant="body2" sx={{ color: '#cccccc' }}>
              Alerts: {systemStats.alertsSent24h}
            </Typography>
            <Typography variant="body2" sx={{ color: '#cccccc' }}>
              Accuracy: {systemStats.detectionRate}%
            </Typography>
          </Box>
        )}

        {/* Navigation Items */}
        <Box sx={{ flexGrow: 1, display: 'flex', gap: 1 }}>
          {navItems.map((item) => (
            <Button
              key={item.path}
              startIcon={
                item.path === '/alerts' ? (
                  <Badge badgeContent={unreadAlerts} color="error">
                    {item.icon}
                  </Badge>
                ) : (
                  item.icon
                )
              }
              onClick={() => navigate(item.path)}
              sx={{
                color: location.pathname === item.path ? '#00ff88' : '#cccccc',
                backgroundColor: location.pathname === item.path ? 'rgba(0, 255, 136, 0.1)' : 'transparent',
                '&:hover': {
                  backgroundColor: 'rgba(0, 255, 136, 0.05)',
                },
              }}
            >
              {item.label}
            </Button>
          ))}
        </Box>

        {/* Right side actions */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <IconButton
            onClick={() => navigate('/alerts')}
            sx={{ color: unreadAlerts > 0 ? '#ff4444' : '#cccccc' }}
          >
            <Badge badgeContent={unreadAlerts} color="error">
              <Notifications />
            </Badge>
          </IconButton>
        </Box>
      </Toolbar>
    </AppBar>
  );
};

export default Navbar;
