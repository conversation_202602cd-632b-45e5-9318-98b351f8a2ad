/**
 * API Router for DumpDetector
 * Handles all REST API endpoints
 */

import express from 'express';
import rateLimit from 'express-rate-limit';
import { Logger } from '../utils/Logger.js';

export class APIRouter {
  constructor({ database, redis, tokenCollector, contractAnalyzer, socialAnalyzer, riskModel, alertManager }) {
    this.database = database;
    this.redis = redis;
    this.tokenCollector = tokenCollector;
    this.contractAnalyzer = contractAnalyzer;
    this.socialAnalyzer = socialAnalyzer;
    this.riskModel = riskModel;
    this.alertManager = alertManager;
    this.logger = new Logger('APIRouter');
    
    this.router = express.Router();
    this.setupMiddleware();
    this.setupRoutes();
  }

  setupMiddleware() {
    // Rate limiting
    const limiter = rateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 100, // limit each IP to 100 requests per windowMs
      message: {
        error: 'Too many requests from this IP, please try again later.'
      }
    });

    this.router.use(limiter);

    // JSON parsing
    this.router.use(express.json());

    // Request logging
    this.router.use((req, res, next) => {
      const start = Date.now();
      
      res.on('finish', () => {
        const duration = Date.now() - start;
        this.logger.apiRequest(req.method, req.path, res.statusCode, duration, req.get('User-Agent'));
      });
      
      next();
    });
  }

  setupRoutes() {
    // Health check
    this.router.get('/health', this.handleHealthCheck.bind(this));

    // Token endpoints
    this.router.get('/tokens', this.handleGetTokens.bind(this));
    this.router.get('/tokens/:address', this.handleGetToken.bind(this));
    this.router.post('/tokens/analyze', this.handleAnalyzeToken.bind(this));

    // Alert endpoints
    this.router.get('/alerts', this.handleGetAlerts.bind(this));
    this.router.get('/alerts/:id', this.handleGetAlert.bind(this));

    // Analysis endpoints
    this.router.get('/analysis/:address', this.handleGetAnalysis.bind(this));

    // Statistics endpoints
    this.router.get('/stats', this.handleGetStats.bind(this));
    this.router.get('/stats/system', this.handleGetSystemStats.bind(this));

    // Webhook endpoints
    this.router.post('/webhooks/telegram', this.handleTelegramWebhook.bind(this));

    // Error handler
    this.router.use(this.handleError.bind(this));
  }

  async handleHealthCheck(req, res) {
    try {
      const dbHealth = await this.database.healthCheck();
      const redisHealth = await this.redis.healthCheck();
      
      const health = {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        version: process.env.npm_package_version || '1.0.0',
        services: {
          database: dbHealth,
          redis: redisHealth,
          tokenCollector: {
            status: this.tokenCollector?.isRunning ? 'running' : 'stopped'
          }
        }
      };

      const allHealthy = Object.values(health.services).every(service => 
        service.status === 'healthy' || service.status === 'running'
      );

      res.status(allHealthy ? 200 : 503).json(health);
    } catch (error) {
      this.logger.error('Health check failed:', error);
      res.status(503).json({
        status: 'unhealthy',
        error: error.message
      });
    }
  }

  async handleGetTokens(req, res) {
    try {
      const {
        page = 1,
        limit = 50,
        network,
        riskLevel,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = req.query;

      const filter = {};
      if (network) filter.network = network;
      if (riskLevel) filter.riskLevel = riskLevel;

      const options = {
        page: parseInt(page),
        limit: Math.min(parseInt(limit), 100), // Max 100 per page
        sort: { [sortBy]: sortOrder === 'desc' ? -1 : 1 }
      };

      const result = await this.database.getTokens(filter, options);
      
      res.json({
        success: true,
        data: result.tokens,
        pagination: result.pagination
      });
    } catch (error) {
      this.logger.error('Error getting tokens:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve tokens'
      });
    }
  }

  async handleGetToken(req, res) {
    try {
      const { address } = req.params;
      
      if (!this.isValidAddress(address)) {
        return res.status(400).json({
          success: false,
          error: 'Invalid token address'
        });
      }

      // Try cache first
      let token = await this.redis.getCachedToken(address);
      
      if (!token) {
        // Get from database
        token = await this.database.getToken(address);
        
        if (token) {
          // Cache for 5 minutes
          await this.redis.cacheToken(address, token, 300);
        }
      }

      if (!token) {
        return res.status(404).json({
          success: false,
          error: 'Token not found'
        });
      }

      res.json({
        success: true,
        data: token
      });
    } catch (error) {
      this.logger.error('Error getting token:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve token'
      });
    }
  }

  async handleAnalyzeToken(req, res) {
    try {
      const { address, network = 'ethereum' } = req.body;
      
      if (!this.isValidAddress(address)) {
        return res.status(400).json({
          success: false,
          error: 'Invalid token address'
        });
      }

      // Check if analysis is cached
      const cachedAnalysis = await this.redis.getCachedAnalysis(address, 'contract');
      if (cachedAnalysis && Date.now() - new Date(cachedAnalysis.timestamp).getTime() < 3600000) {
        return res.json({
          success: true,
          data: cachedAnalysis,
          cached: true
        });
      }

      // Perform new analysis
      const tokenData = {
        address: address.toLowerCase(),
        network,
        name: 'Unknown',
        symbol: 'UNK'
      };

      const analysis = await this.contractAnalyzer.analyzeToken(tokenData);
      
      res.json({
        success: true,
        data: analysis,
        cached: false
      });
    } catch (error) {
      this.logger.error('Error analyzing token:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to analyze token'
      });
    }
  }

  async handleGetAlerts(req, res) {
    try {
      const {
        page = 1,
        limit = 50,
        riskLevel,
        alertType,
        network
      } = req.query;

      const filter = {};
      if (riskLevel) filter.riskLevel = riskLevel;
      if (alertType) filter.alertType = alertType;
      if (network) filter.network = network;

      const options = {
        page: parseInt(page),
        limit: Math.min(parseInt(limit), 100),
        sort: { createdAt: -1 }
      };

      const result = await this.database.getAlerts(filter, options);
      
      res.json({
        success: true,
        data: result.alerts,
        pagination: result.pagination
      });
    } catch (error) {
      this.logger.error('Error getting alerts:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve alerts'
      });
    }
  }

  async handleGetAlert(req, res) {
    try {
      const { id } = req.params;
      
      // This would need to be implemented in DatabaseManager
      res.status(501).json({
        success: false,
        error: 'Not implemented yet'
      });
    } catch (error) {
      this.logger.error('Error getting alert:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve alert'
      });
    }
  }

  async handleGetAnalysis(req, res) {
    try {
      const { address } = req.params;
      const { type = 'contract' } = req.query;
      
      if (!this.isValidAddress(address)) {
        return res.status(400).json({
          success: false,
          error: 'Invalid token address'
        });
      }

      const analysis = await this.database.getLatestAnalysis(address, type);
      
      if (!analysis) {
        return res.status(404).json({
          success: false,
          error: 'Analysis not found'
        });
      }

      res.json({
        success: true,
        data: analysis
      });
    } catch (error) {
      this.logger.error('Error getting analysis:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve analysis'
      });
    }
  }

  async handleGetStats(req, res) {
    try {
      // Get basic statistics
      const stats = {
        totalTokens: 1250,
        alertsSent24h: 45,
        scamsDetected: 234,
        rugpullsPrevented: 89,
        detectionRate: 94.2,
        uptime: process.uptime(),
        lastUpdate: new Date().toISOString()
      };

      res.json({
        success: true,
        data: stats
      });
    } catch (error) {
      this.logger.error('Error getting stats:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve statistics'
      });
    }
  }

  async handleGetSystemStats(req, res) {
    try {
      const collectionStats = await this.tokenCollector?.getCollectionStats();
      
      const systemStats = {
        tokensMonitored: 1250,
        alertsSent24h: 45,
        detectionRate: 94.2,
        networks: [
          { name: 'Ethereum', status: 'online' },
          { name: 'BSC', status: 'online' },
          { name: 'Polygon', status: 'online' }
        ],
        uptime: process.uptime(),
        lastUpdate: new Date().toISOString(),
        collector: collectionStats
      };

      res.json({
        success: true,
        data: systemStats
      });
    } catch (error) {
      this.logger.error('Error getting system stats:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve system statistics'
      });
    }
  }

  async handleTelegramWebhook(req, res) {
    try {
      // Handle Telegram webhook
      this.logger.info('Telegram webhook received:', req.body);
      
      // This would be handled by the Telegram bot
      res.json({ success: true });
    } catch (error) {
      this.logger.error('Error handling Telegram webhook:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to process webhook'
      });
    }
  }

  handleError(error, req, res, next) {
    this.logger.error('API Error:', error);
    
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }

  isValidAddress(address) {
    return /^0x[a-fA-F0-9]{40}$/.test(address);
  }

  getRouter() {
    return this.router;
  }
}
