#!/usr/bin/env node

/**
 * Test AI Report Generation
 * Test the GLM-4.5-Air model with USDT data
 */

import https from 'https';

const OPENROUTER_API_KEY = '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';

// Make POST request for AI API
function makePostRequest(url, data, headers = {}) {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify(data);
    const urlObj = new URL(url);
    
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port || 443,
      path: urlObj.pathname,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': postData.length,
        ...headers
      }
    };

    const req = https.request(options, (res) => {
      let responseData = '';
      res.on('data', (chunk) => responseData += chunk);
      res.on('end', () => {
        try {
          resolve(JSON.parse(responseData));
        } catch (error) {
          reject(new Error(`Failed to parse response: ${error.message}`));
        }
      });
    });

    req.on('error', reject);
    req.setTimeout(60000, () => { // Increased timeout to 60 seconds
      req.destroy();
      reject(new Error('AI request timeout'));
    });

    req.write(postData);
    req.end();
  });
}

// Test AI report generation
async function testAIReport() {
  try {
    console.log('🤖 Testing AI Report Generation...');
    console.log('📡 Using OpenRouter API with GLM-4.5-Air model');
    console.log('⏱️  This may take 30-60 seconds...');
    
    const testPrompt = `
COMPREHENSIVE CRYPTOCURRENCY TOKEN ANALYSIS REQUEST

Please conduct a thorough security and investment analysis for the following cryptocurrency token:

=== TOKEN IDENTIFICATION ===
Contract Address: ******************************************
Token Name: Tether USD
Symbol: USDT
Blockchain Network: ETHEREUM
Decimals: 6
Total Supply: 120,000,000,000 (120 billion)

=== CONTRACT VERIFICATION DATA ===
Source Code Verified: YES
Contract Name: TetherToken
Compiler Version: v0.4.18+commit.9cf6e910
Proxy Contract: NO
Source Code Available: YES (Verified on Etherscan)

=== HOLDER DISTRIBUTION ANALYSIS ===
Total Holders: 5,200,000+ addresses
Top 10 Concentration: ~15% (relatively distributed)
Data Quality: REAL API DATA
Market Cap: $120+ billion (largest stablecoin)

=== ANALYSIS REQUIREMENTS ===

Please provide a comprehensive analysis covering:

1. **SECURITY ASSESSMENT (0-100 Score)**
   - Contract security vulnerabilities
   - Centralization risks (Tether Limited control)
   - Regulatory compliance and reserves
   - Technical implementation security

2. **MARKET RISK ANALYSIS**
   - Stablecoin peg stability risks
   - Regulatory and legal risks
   - Market liquidity and adoption
   - Competition from other stablecoins

3. **TECHNICAL EVALUATION**
   - Smart contract architecture
   - ERC-20 standard compliance
   - Upgrade mechanisms and governance
   - Integration across DeFi protocols

4. **INVESTMENT RECOMMENDATION**
   - Overall risk level (LOW/MEDIUM/HIGH/CRITICAL)
   - Specific risk factors for USDT
   - Use case suitability assessment
   - Comparison with other stablecoins

5. **REGULATORY ANALYSIS**
   - Tether Limited's legal status
   - Reserve backing transparency
   - Regulatory compliance across jurisdictions
   - Recent legal developments

6. **FUTURE OUTLOOK**
   - Market position sustainability
   - Regulatory pressure impact
   - Competition from CBDCs
   - Long-term viability assessment

=== OUTPUT FORMAT ===
Please structure your response with clear sections and provide:
- Executive Summary (2-3 sentences)
- Security Score (0-100)
- Risk Level (LOW/MEDIUM/HIGH/CRITICAL)
- Key Findings (bullet points)
- Detailed Analysis (comprehensive breakdown)
- Final Recommendation (clear guidance)

Focus on factual analysis based on USDT's known characteristics, regulatory challenges, and market position.
`;

    const startTime = Date.now();
    
    const aiResponse = await makePostRequest(
      'https://openrouter.ai/api/v1/chat/completions',
      {
        model: 'z-ai/glm-4.5-air:free',
        messages: [
          {
            role: 'system',
            content: 'You are a senior cryptocurrency security analyst and blockchain researcher with expertise in stablecoin analysis, regulatory compliance, and DeFi risk assessment. Provide detailed, professional analysis based on the provided data.'
          },
          {
            role: 'user',
            content: testPrompt
          }
        ],
        max_tokens: 2500,
        temperature: 0.2
      },
      {
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'HTTP-Referer': 'https://dumpdetector.com',
        'X-Title': 'DumpDetector AI Test'
      }
    );

    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;

    console.log(`✅ AI Report Generated Successfully!`);
    console.log(`⏱️  Generation Time: ${duration.toFixed(2)} seconds`);
    console.log('📊 Report Length:', aiResponse.choices[0].message.content.length, 'characters');
    console.log('\n' + '='.repeat(80));
    console.log('🤖 AI GENERATED REPORT:');
    console.log('='.repeat(80));
    console.log(aiResponse.choices[0].message.content);
    console.log('='.repeat(80));

    return {
      success: true,
      report: aiResponse.choices[0].message.content,
      duration: duration,
      length: aiResponse.choices[0].message.content.length
    };

  } catch (error) {
    console.error('❌ AI Report Generation Failed:', error.message);
    return {
      success: false,
      error: error.message
    };
  }
}

// Test real token data fetching
async function testRealTokenData() {
  try {
    console.log('\n🔍 Testing Real Token Data Fetching...');
    
    // Test with environment variable
    const INFURA_PROJECT_ID = process.env.INFURA_PROJECT_ID || '********************************';
    console.log('🔑 Using Infura Project ID:', INFURA_PROJECT_ID.substring(0, 8) + '...');
    
    // Make RPC call to get USDT name
    const rpcUrl = `https://mainnet.infura.io/v3/${INFURA_PROJECT_ID}`;
    const usdtAddress = '******************************************';
    const nameSignature = '0x06fdde03'; // name() function signature
    
    const rpcData = JSON.stringify({
      jsonrpc: '2.0',
      method: 'eth_call',
      params: [
        {
          to: usdtAddress,
          data: nameSignature
        },
        'latest'
      ],
      id: 1
    });

    console.log('📡 Making RPC call to fetch USDT name...');
    
    const response = await new Promise((resolve, reject) => {
      const url = new URL(rpcUrl);
      const options = {
        hostname: url.hostname,
        port: url.port || 443,
        path: url.pathname,
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Content-Length': rpcData.length
        }
      };

      const req = https.request(options, (res) => {
        let responseData = '';
        res.on('data', (chunk) => responseData += chunk);
        res.on('end', () => {
          try {
            resolve(JSON.parse(responseData));
          } catch (error) {
            reject(new Error(`Failed to parse RPC response: ${error.message}`));
          }
        });
      });

      req.on('error', reject);
      req.setTimeout(10000, () => {
        req.destroy();
        reject(new Error('RPC request timeout'));
      });

      req.write(rpcData);
      req.end();
    });

    if (response.result) {
      // Convert hex to string
      const hex = response.result.replace('0x', '');
      let tokenName = '';
      
      // Skip first 64 characters (offset and length)
      const dataHex = hex.substring(128);
      
      for (let i = 0; i < dataHex.length; i += 2) {
        const charCode = parseInt(dataHex.substr(i, 2), 16);
        if (charCode > 0 && charCode < 127) {
          tokenName += String.fromCharCode(charCode);
        }
      }
      
      console.log('✅ Real Token Data Fetched Successfully!');
      console.log('📊 Token Name:', tokenName.trim());
      console.log('🔗 Contract Address:', usdtAddress);
      
      return {
        success: true,
        tokenName: tokenName.trim(),
        address: usdtAddress
      };
    } else {
      throw new Error('No result in RPC response');
    }

  } catch (error) {
    console.error('❌ Real Token Data Fetch Failed:', error.message);
    return {
      success: false,
      error: error.message
    };
  }
}

// Run tests
async function runTests() {
  console.log('🧪 Starting DumpDetector Tests...\n');
  
  // Test 1: Real token data fetching
  const tokenTest = await testRealTokenData();
  
  // Test 2: AI report generation
  const aiTest = await testAIReport();
  
  console.log('\n📋 TEST RESULTS SUMMARY:');
  console.log('========================');
  console.log('🔍 Real Token Data:', tokenTest.success ? '✅ PASS' : '❌ FAIL');
  if (tokenTest.success) {
    console.log('   Token Name:', tokenTest.tokenName);
  } else {
    console.log('   Error:', tokenTest.error);
  }
  
  console.log('🤖 AI Report Generation:', aiTest.success ? '✅ PASS' : '❌ FAIL');
  if (aiTest.success) {
    console.log('   Duration:', aiTest.duration.toFixed(2), 'seconds');
    console.log('   Report Length:', aiTest.length, 'characters');
  } else {
    console.log('   Error:', aiTest.error);
  }
  
  if (tokenTest.success && aiTest.success) {
    console.log('\n🎉 All tests passed! Ready to implement enhanced analyzer.');
  } else {
    console.log('\n⚠️  Some tests failed. Check the errors above.');
  }
}

// Run the tests
runTests().catch(console.error);
