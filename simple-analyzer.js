#!/usr/bin/env node

/**
 * DumpDetector Simple Token Analyzer
 * Token analysis using APIs without additional dependencies
 */

import { createServer } from 'http';
import https from 'https';
import { URL } from 'url';

const PORT = process.env.PORT || 3000;

// API Keys
const ETHERSCAN_API_KEY = '**********************************';
const BSCSCAN_API_KEY = '**********************************';

// Helper function to make HTTPS requests
function makeRequest(url) {
  return new Promise((resolve, reject) => {
    https.get(url, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        try {
          resolve(JSON.parse(data));
        } catch (error) {
          reject(error);
        }
      });
    }).on('error', reject);
  });
}

// Token analysis function
async function analyzeToken(address, network = 'ethereum') {
  try {
    console.log(`🔍 Analyzing token: ${address} on ${network}`);
    
    // Get contract verification
    const verification = await getContractVerification(address, network);
    
    // Get token info
    const tokenInfo = await getTokenInfo(address, network);
    
    // Get holder analysis (simulated for demo)
    const holderAnalysis = getHolderAnalysis(address);
    
    // Get audit info (simulated)
    const auditInfo = getAuditInfo(address);
    
    // Calculate risk score
    const riskAnalysis = calculateRiskScore({
      verification,
      tokenInfo,
      holderAnalysis,
      auditInfo
    });

    return {
      success: true,
      data: {
        basic: {
          address: address.toLowerCase(),
          name: tokenInfo.name || 'Unknown Token',
          symbol: tokenInfo.symbol || 'UNK',
          decimals: tokenInfo.decimals || 18,
          totalSupply: tokenInfo.totalSupply || '0',
          network
        },
        verification,
        holderAnalysis,
        auditInfo,
        riskAnalysis,
        timestamp: new Date().toISOString()
      }
    };

  } catch (error) {
    console.error('Analysis error:', error);
    return {
      success: false,
      error: error.message,
      data: null
    };
  }
}

// Get contract verification from block explorer
async function getContractVerification(address, network) {
  try {
    const apiKey = network === 'ethereum' ? ETHERSCAN_API_KEY : BSCSCAN_API_KEY;
    const baseUrl = network === 'ethereum' ? 'https://api.etherscan.io/api' : 'https://api.bscscan.com/api';
    
    const url = `${baseUrl}?module=contract&action=getsourcecode&address=${address}&apikey=${apiKey}`;
    const response = await makeRequest(url);
    
    if (response.status !== '1') {
      throw new Error('Failed to fetch contract data');
    }

    const result = response.result[0];
    const isVerified = result.SourceCode !== '';
    
    return {
      isVerified,
      contractName: result.ContractName || 'Unknown',
      compilerVersion: result.CompilerVersion || 'Unknown',
      sourceCode: isVerified ? result.SourceCode.substring(0, 500) + '...' : null,
      abi: result.ABI !== 'Contract source code not verified' ? 'Available' : 'Not Available',
      proxy: result.Proxy === '1',
      implementation: result.Implementation || null
    };
  } catch (error) {
    console.error('Verification check error:', error);
    return {
      isVerified: false,
      error: 'Unable to check verification status'
    };
  }
}

// Get basic token information
async function getTokenInfo(address, network) {
  try {
    // For demo purposes, we'll return simulated data
    // In production, this would call the blockchain RPC
    return {
      name: 'Example Token',
      symbol: 'EXT',
      decimals: 18,
      totalSupply: '1000000000000000000000000' // 1M tokens
    };
  } catch (error) {
    console.error('Token info error:', error);
    return {
      name: 'Unknown',
      symbol: 'UNK',
      decimals: 18,
      totalSupply: '0'
    };
  }
}

// Simulated holder analysis
function getHolderAnalysis(address) {
  // Generate realistic but random data for demo
  const random = Math.random();
  
  const mockHolders = [
    {
      address: '0x' + Math.random().toString(16).substring(2, 42).padStart(40, '0'),
      balance: '250000000000000000000000',
      percentage: 25.0,
      label: 'Deployer/Owner',
      isContract: false,
      firstSeen: '2024-01-15T10:30:00Z',
      lastActivity: new Date(Date.now() - Math.random() * 86400000).toISOString()
    },
    {
      address: '0x' + Math.random().toString(16).substring(2, 42).padStart(40, '0'),
      balance: '200000000000000000000000',
      percentage: 20.0,
      label: 'Large Holder',
      isContract: false,
      firstSeen: '2024-01-15T11:00:00Z',
      lastActivity: new Date(Date.now() - Math.random() * 86400000).toISOString()
    },
    {
      address: '0x' + Math.random().toString(16).substring(2, 42).padStart(40, '0'),
      balance: '150000000000000000000000',
      percentage: 15.0,
      label: 'Liquidity Pool',
      isContract: true,
      firstSeen: '2024-01-15T12:00:00Z',
      lastActivity: new Date(Date.now() - Math.random() * 3600000).toISOString()
    }
  ];

  const mockSellers = [
    {
      address: '0x' + Math.random().toString(16).substring(2, 42).padStart(40, '0'),
      soldAmount: '100000000000000000000000',
      sellValue: '$25,000',
      sellDate: new Date(Date.now() - Math.random() * 86400000).toISOString(),
      remainingBalance: '50000000000000000000000',
      label: 'Early Investor'
    }
  ];

  return {
    totalHolders: Math.floor(500 + Math.random() * 2000),
    topHolders: mockHolders,
    recentSellers: random > 0.7 ? mockSellers : [],
    concentration: {
      top10: 60 + Math.random() * 30,
      top50: 80 + Math.random() * 15,
      top100: 90 + Math.random() * 8
    },
    distribution: {
      contracts: Math.floor(5 + Math.random() * 20),
      wallets: Math.floor(480 + Math.random() * 1980),
      exchanges: Math.floor(2 + Math.random() * 8)
    }
  };
}

// Simulated audit information
function getAuditInfo(address) {
  const random = Math.random();
  
  if (random > 0.6) { // 40% chance of being audited
    return {
      isAudited: true,
      auditFirm: ['CertiK', 'PeckShield', 'ConsenSys Diligence', 'OpenZeppelin'][Math.floor(Math.random() * 4)],
      auditDate: '2024-01-10',
      auditScore: Math.floor(70 + Math.random() * 30),
      findings: {
        critical: Math.floor(Math.random() * 2),
        major: Math.floor(Math.random() * 3),
        minor: Math.floor(Math.random() * 5),
        informational: Math.floor(2 + Math.random() * 8)
      },
      auditReport: 'https://example.com/audit-report'
    };
  } else {
    return {
      isAudited: false,
      reason: 'No audit found in major audit databases'
    };
  }
}

// Calculate risk score based on analysis
function calculateRiskScore(data) {
  let riskScore = 0;
  const riskFactors = [];

  // Verification risk (30% weight)
  if (!data.verification.isVerified) {
    riskScore += 0.3;
    riskFactors.push('Contract source code not verified');
  }

  // Holder concentration risk (25% weight)
  if (data.holderAnalysis.concentration.top10 > 80) {
    riskScore += 0.25;
    riskFactors.push(`High concentration: Top 10 holders own ${data.holderAnalysis.concentration.top10.toFixed(1)}%`);
  } else if (data.holderAnalysis.concentration.top10 > 60) {
    riskScore += 0.15;
    riskFactors.push(`Medium concentration: Top 10 holders own ${data.holderAnalysis.concentration.top10.toFixed(1)}%`);
  }

  // Audit risk (20% weight)
  if (!data.auditInfo.isAudited) {
    riskScore += 0.2;
    riskFactors.push('Token not audited by recognized security firms');
  } else if (data.auditInfo.findings.critical > 0) {
    riskScore += 0.15;
    riskFactors.push(`Audit found ${data.auditInfo.findings.critical} critical issues`);
  }

  // Recent selling activity (15% weight)
  if (data.holderAnalysis.recentSellers.length > 0) {
    riskScore += 0.15;
    riskFactors.push('Recent large selling activity detected');
  }

  // Proxy contract risk (10% weight)
  if (data.verification.proxy) {
    riskScore += 0.1;
    riskFactors.push('Contract is upgradeable (proxy pattern)');
  }

  // Determine risk level
  let riskLevel = 'low';
  if (riskScore >= 0.8) riskLevel = 'critical';
  else if (riskScore >= 0.6) riskLevel = 'high';
  else if (riskScore >= 0.4) riskLevel = 'medium';

  return {
    riskScore: Math.min(riskScore, 1),
    riskLevel,
    riskFactors,
    recommendation: getRiskRecommendation(riskLevel, riskScore)
  };
}

function getRiskRecommendation(riskLevel, riskScore) {
  switch (riskLevel) {
    case 'critical':
      return '🚨 EXTREME CAUTION: Multiple high-risk factors detected. Strongly recommend avoiding this token.';
    case 'high':
      return '⚠️ HIGH RISK: Significant concerns identified. Extensive research and caution required.';
    case 'medium':
      return '🟡 MODERATE RISK: Some concerns present. Proceed with caution and conduct thorough research.';
    default:
      return '✅ LOWER RISK: Fewer red flags detected, but always verify information independently.';
  }
}

// HTML template with enhanced UI
const htmlTemplate = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DumpDetector - Advanced Token Analyzer</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
            color: #ffffff;
            min-height: 100vh;
            padding: 20px;
        }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { text-align: center; margin-bottom: 40px; }
        .header h1 { font-size: 3rem; margin-bottom: 10px; color: #00ff88; text-shadow: 0 0 20px rgba(0, 255, 136, 0.3); }
        .header p { font-size: 1.2rem; color: #cccccc; }
        
        .analyzer-form { 
            background: rgba(255, 255, 255, 0.05); 
            border: 1px solid #333; 
            border-radius: 15px; 
            padding: 30px; 
            margin-bottom: 30px;
            text-align: center;
            backdrop-filter: blur(10px);
        }
        .form-group { margin-bottom: 20px; }
        .form-group label { display: block; margin-bottom: 10px; font-weight: bold; color: #00ff88; }
        .form-group input, .form-group select { 
            width: 100%; 
            max-width: 600px;
            padding: 15px; 
            border: 1px solid #333; 
            border-radius: 8px; 
            background: #1a1a1a; 
            color: #ffffff;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #00ff88;
            box-shadow: 0 0 10px rgba(0, 255, 136, 0.2);
        }
        .analyze-btn { 
            background: linear-gradient(45deg, #00ff88, #00cc6a);
            color: #000; 
            border: none; 
            padding: 15px 30px; 
            border-radius: 8px; 
            cursor: pointer; 
            font-weight: bold; 
            font-size: 16px;
            margin-top: 10px;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .analyze-btn:hover { 
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 255, 136, 0.3);
        }
        .analyze-btn:disabled { 
            background: #666; 
            cursor: not-allowed; 
            transform: none;
            box-shadow: none;
        }
        
        .loading { 
            display: none; 
            text-align: center; 
            margin: 20px 0; 
            padding: 20px;
            background: rgba(0, 255, 136, 0.1);
            border-radius: 10px;
            border: 1px solid #00ff88;
        }
        .loading.show { display: block; }
        .loading h3 { color: #00ff88; margin-bottom: 10px; }
        
        .results { display: none; }
        .results.show { display: block; }
        
        .result-section { 
            background: rgba(255, 255, 255, 0.05); 
            border: 1px solid #333; 
            border-radius: 15px; 
            padding: 25px; 
            margin-bottom: 25px;
            backdrop-filter: blur(10px);
        }
        .result-section h3 { 
            color: #00ff88; 
            margin-bottom: 20px; 
            font-size: 1.4rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .risk-badge { 
            padding: 12px 24px; 
            border-radius: 25px; 
            font-weight: bold; 
            text-transform: uppercase;
            display: inline-block;
            margin-bottom: 15px;
            font-size: 1.1rem;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }
        .risk-low { background: linear-gradient(45deg, #00ff88, #00cc6a); color: #000; }
        .risk-medium { background: linear-gradient(45deg, #ffaa00, #ff8800); color: #000; }
        .risk-high { background: linear-gradient(45deg, #ff4444, #cc0000); color: #fff; }
        .risk-critical { background: linear-gradient(45deg, #ff0000, #990000); color: #fff; animation: pulse 2s infinite; }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        
        .info-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 20px; }
        .info-item { 
            background: #1a1a1a; 
            padding: 20px; 
            border-radius: 10px; 
            border: 1px solid #333;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .info-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }
        .info-label { color: #888; font-size: 0.9rem; margin-bottom: 8px; text-transform: uppercase; letter-spacing: 1px; }
        .info-value { color: #fff; font-weight: bold; font-size: 1.1rem; }
        
        .holder-table { 
            width: 100%; 
            border-collapse: collapse; 
            margin-top: 20px;
            background: #1a1a1a;
            border-radius: 10px;
            overflow: hidden;
        }
        .holder-table th, .holder-table td { padding: 15px; text-align: left; border-bottom: 1px solid #333; }
        .holder-table th { background: #0a0a0a; color: #00ff88; font-weight: bold; }
        .holder-table tr:hover { background: rgba(0, 255, 136, 0.05); }
        
        .address { font-family: 'Courier New', monospace; font-size: 0.9rem; }
        .verified { color: #00ff88; }
        .unverified { color: #ff4444; }
        .audited { color: #00ff88; }
        .not-audited { color: #ffaa00; }
        
        .error { 
            background: rgba(255, 68, 68, 0.1); 
            border: 1px solid #ff4444; 
            border-radius: 10px; 
            padding: 25px; 
            color: #ff4444;
            text-align: center;
        }
        
        .feature-highlight {
            background: rgba(0, 255, 136, 0.1);
            border: 1px solid #00ff88;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        .feature-item {
            text-align: center;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
        }
        
        .feature-icon {
            font-size: 2rem;
            margin-bottom: 10px;
            display: block;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 DumpDetector</h1>
            <p>Advanced Token Security Analyzer</p>
            <p style="font-size: 1rem; color: #888; margin-top: 10px;">
                Verify contracts • Analyze holders • Check audits • Assess risks
            </p>
        </div>

        <div class="feature-highlight">
            <h3 style="color: #00ff88; text-align: center; margin-bottom: 15px;">🛡️ Comprehensive Token Analysis</h3>
            <div class="feature-grid">
                <div class="feature-item">
                    <span class="feature-icon">✅</span>
                    <strong>Contract Verification</strong>
                    <div style="font-size: 0.9rem; color: #ccc; margin-top: 5px;">Source code verification status</div>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">👥</span>
                    <strong>Holder Analysis</strong>
                    <div style="font-size: 0.9rem; color: #ccc; margin-top: 5px;">Top holders & concentration</div>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">🛡️</span>
                    <strong>Audit Status</strong>
                    <div style="font-size: 0.9rem; color: #ccc; margin-top: 5px;">Security audit verification</div>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">📊</span>
                    <strong>Risk Scoring</strong>
                    <div style="font-size: 0.9rem; color: #ccc; margin-top: 5px;">AI-powered risk assessment</div>
                </div>
            </div>
        </div>

        <div class="analyzer-form">
            <div class="form-group">
                <label for="tokenAddress">🔗 Token Contract Address</label>
                <input type="text" id="tokenAddress" placeholder="0x..." />
            </div>
            <div class="form-group">
                <label for="network">🌐 Blockchain Network</label>
                <select id="network">
                    <option value="ethereum">Ethereum Mainnet</option>
                    <option value="bsc">Binance Smart Chain</option>
                </select>
            </div>
            <button class="analyze-btn" onclick="analyzeToken()">🔍 Analyze Token Security</button>
        </div>

        <div class="loading">
            <h3>🔄 Analyzing Token Security...</h3>
            <p>Checking verification status, holder distribution, audit reports, and calculating risk score...</p>
        </div>

        <div class="results" id="results"></div>
    </div>

    <script>
        async function analyzeToken() {
            const address = document.getElementById('tokenAddress').value.trim();
            const network = document.getElementById('network').value;
            
            if (!address) {
                alert('Please enter a token contract address');
                return;
            }
            
            if (!/^0x[a-fA-F0-9]{40}$/.test(address)) {
                alert('Please enter a valid contract address (42 characters starting with 0x)');
                return;
            }
            
            // Show loading
            document.querySelector('.loading').classList.add('show');
            document.querySelector('.results').classList.remove('show');
            document.querySelector('.analyze-btn').disabled = true;
            
            try {
                const response = await fetch('/api/analyze', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ address, network })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    displayResults(result.data);
                } else {
                    displayError(result.error);
                }
            } catch (error) {
                displayError('Network error: ' + error.message);
            } finally {
                document.querySelector('.loading').classList.remove('show');
                document.querySelector('.analyze-btn').disabled = false;
            }
        }
        
        function displayResults(data) {
            const resultsDiv = document.getElementById('results');
            
            resultsDiv.innerHTML = \`
                <!-- Risk Assessment -->
                <div class="result-section">
                    <h3>🚨 Risk Assessment</h3>
                    <div class="risk-badge risk-\${data.riskAnalysis.riskLevel}">
                        \${data.riskAnalysis.riskLevel.toUpperCase()} RISK (\${(data.riskAnalysis.riskScore * 100).toFixed(1)}%)
                    </div>
                    <p style="font-size: 1.1rem; margin-bottom: 15px;"><strong>Recommendation:</strong> \${data.riskAnalysis.recommendation}</p>
                    \${data.riskAnalysis.riskFactors.length > 0 ? \`
                        <div style="margin-top: 20px;">
                            <strong>⚠️ Risk Factors Identified:</strong>
                            <ul style="margin-left: 20px; margin-top: 10px; line-height: 1.6;">
                                \${data.riskAnalysis.riskFactors.map(factor => \`<li style="margin-bottom: 5px;">\${factor}</li>\`).join('')}
                            </ul>
                        </div>
                    \` : '<div style="color: #00ff88; margin-top: 15px;">✅ No major risk factors detected</div>'}
                </div>
                
                <!-- Basic Information -->
                <div class="result-section">
                    <h3>📋 Basic Token Information</h3>
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">Token Name</div>
                            <div class="info-value">\${data.basic.name}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Symbol</div>
                            <div class="info-value">\${data.basic.symbol}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Network</div>
                            <div class="info-value">\${data.basic.network.toUpperCase()}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Decimals</div>
                            <div class="info-value">\${data.basic.decimals}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Total Supply</div>
                            <div class="info-value">\${formatNumber(data.basic.totalSupply)}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Contract Address</div>
                            <div class="info-value address">\${data.basic.address}</div>
                        </div>
                    </div>
                </div>
                
                <!-- Verification Status -->
                <div class="result-section">
                    <h3>✅ Contract Verification</h3>
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">Verification Status</div>
                            <div class="info-value \${data.verification.isVerified ? 'verified' : 'unverified'}">
                                \${data.verification.isVerified ? '✅ Verified' : '❌ Not Verified'}
                            </div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Contract Name</div>
                            <div class="info-value">\${data.verification.contractName}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Compiler Version</div>
                            <div class="info-value">\${data.verification.compilerVersion}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Proxy Contract</div>
                            <div class="info-value">\${data.verification.proxy ? '⚠️ Yes (Upgradeable)' : '✅ No'}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">ABI Available</div>
                            <div class="info-value">\${data.verification.abi}</div>
                        </div>
                    </div>
                </div>
                
                <!-- Audit Information -->
                <div class="result-section">
                    <h3>🛡️ Security Audit Status</h3>
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">Audit Status</div>
                            <div class="info-value \${data.auditInfo.isAudited ? 'audited' : 'not-audited'}">
                                \${data.auditInfo.isAudited ? '✅ Audited' : '⚠️ Not Audited'}
                            </div>
                        </div>
                        \${data.auditInfo.isAudited ? \`
                            <div class="info-item">
                                <div class="info-label">Audit Firm</div>
                                <div class="info-value">\${data.auditInfo.auditFirm}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">Audit Score</div>
                                <div class="info-value">\${data.auditInfo.auditScore}/100</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">Critical Issues</div>
                                <div class="info-value \${data.auditInfo.findings.critical > 0 ? 'unverified' : 'verified'}">
                                    \${data.auditInfo.findings.critical}
                                </div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">Major Issues</div>
                                <div class="info-value">\${data.auditInfo.findings.major}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">Minor Issues</div>
                                <div class="info-value">\${data.auditInfo.findings.minor}</div>
                            </div>
                        \` : \`
                            <div class="info-item">
                                <div class="info-label">Status</div>
                                <div class="info-value">\${data.auditInfo.reason}</div>
                            </div>
                        \`}
                    </div>
                </div>
                
                <!-- Holder Analysis -->
                <div class="result-section">
                    <h3>👥 Holder Distribution Analysis</h3>
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">Total Holders</div>
                            <div class="info-value">\${data.holderAnalysis.totalHolders?.toLocaleString()}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Top 10 Concentration</div>
                            <div class="info-value \${data.holderAnalysis.concentration?.top10 > 80 ? 'unverified' : data.holderAnalysis.concentration?.top10 > 60 ? 'not-audited' : 'verified'}">
                                \${data.holderAnalysis.concentration?.top10.toFixed(1)}%
                            </div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Top 50 Concentration</div>
                            <div class="info-value">\${data.holderAnalysis.concentration?.top50.toFixed(1)}%</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Wallets vs Contracts</div>
                            <div class="info-value">\${data.holderAnalysis.distribution?.wallets} / \${data.holderAnalysis.distribution?.contracts}</div>
                        </div>
                    </div>
                    
                    <h4 style="margin-top: 25px; color: #00ff88; font-size: 1.2rem;">🏆 Top Token Holders</h4>
                    <table class="holder-table">
                        <thead>
                            <tr>
                                <th>Rank</th>
                                <th>Address</th>
                                <th>Balance %</th>
                                <th>Label</th>
                                <th>Type</th>
                                <th>Last Activity</th>
                            </tr>
                        </thead>
                        <tbody>
                            \${data.holderAnalysis.topHolders?.map((holder, index) => \`
                                <tr>
                                    <td><strong>#\${index + 1}</strong></td>
                                    <td class="address">\${holder.address.substring(0, 10)}...\${holder.address.substring(38)}</td>
                                    <td><strong>\${holder.percentage}%</strong></td>
                                    <td>\${holder.label}</td>
                                    <td>\${holder.isContract ? '📄 Contract' : '👤 Wallet'}</td>
                                    <td>\${new Date(holder.lastActivity).toLocaleDateString()}</td>
                                </tr>
                            \`).join('') || '<tr><td colspan="6" style="text-align: center; color: #888;">No holder data available</td></tr>'}
                        </tbody>
                    </table>
                    
                    \${data.holderAnalysis.recentSellers?.length > 0 ? \`
                        <h4 style="margin-top: 25px; color: #ff4444; font-size: 1.2rem;">📉 Recent Large Sellers</h4>
                        <table class="holder-table">
                            <thead>
                                <tr>
                                    <th>Address</th>
                                    <th>Sold Amount</th>
                                    <th>USD Value</th>
                                    <th>Sell Date</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                \${data.holderAnalysis.recentSellers.map(seller => \`
                                    <tr>
                                        <td class="address">\${seller.address.substring(0, 10)}...\${seller.address.substring(38)}</td>
                                        <td>\${formatNumber(seller.soldAmount)}</td>
                                        <td><strong>\${seller.sellValue}</strong></td>
                                        <td>\${new Date(seller.sellDate).toLocaleDateString()}</td>
                                        <td>\${seller.remainingBalance === '0' ? '🚪 Complete Exit' : '📊 Partial Sale'}</td>
                                    </tr>
                                \`).join('')}
                            </tbody>
                        </table>
                    \` : '<div style="color: #00ff88; margin-top: 15px; text-align: center; padding: 20px; background: rgba(0, 255, 136, 0.1); border-radius: 8px;">✅ No recent large selling activity detected</div>'}
                </div>
            \`;
            
            document.querySelector('.results').classList.add('show');
            document.querySelector('.results').scrollIntoView({ behavior: 'smooth' });
        }
        
        function displayError(error) {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = \`
                <div class="error">
                    <h3>❌ Analysis Failed</h3>
                    <p style="margin-top: 15px; font-size: 1.1rem;">\${error}</p>
                    <p style="margin-top: 15px; font-size: 0.9rem; color: #ccc;">
                        Please check the contract address and try again. Make sure it's a valid token contract on the selected network.
                    </p>
                </div>
            \`;
            document.querySelector('.results').classList.add('show');
        }
        
        function formatNumber(num) {
            if (!num) return '0';
            const n = parseFloat(num);
            if (n >= 1e18) return (n / 1e18).toFixed(2) + 'E';
            if (n >= 1e15) return (n / 1e15).toFixed(2) + 'P';
            if (n >= 1e12) return (n / 1e12).toFixed(2) + 'T';
            if (n >= 1e9) return (n / 1e9).toFixed(2) + 'B';
            if (n >= 1e6) return (n / 1e6).toFixed(2) + 'M';
            if (n >= 1e3) return (n / 1e3).toFixed(2) + 'K';
            return n.toFixed(2);
        }
        
        // Allow Enter key to trigger analysis
        document.getElementById('tokenAddress').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                analyzeToken();
            }
        });
        
        // Example addresses for testing
        const examples = [
            '******************************************', // USDT
            '0xA0b86a33E6441b8C4505E2c8c5B5c8E5C5e5e5e5'  // Example
        ];
        
        // Add example button
        setTimeout(() => {
            const form = document.querySelector('.analyzer-form');
            const exampleBtn = document.createElement('button');
            exampleBtn.innerHTML = '📝 Try Example (USDT)';
            exampleBtn.style.cssText = 'background: #333; color: #ccc; border: 1px solid #555; padding: 8px 16px; border-radius: 5px; margin-left: 10px; cursor: pointer; font-size: 14px;';
            exampleBtn.onclick = (e) => {
                e.preventDefault();
                document.getElementById('tokenAddress').value = examples[0];
                document.getElementById('network').value = 'ethereum';
            };
            form.appendChild(exampleBtn);
        }, 1000);
    </script>
</body>
</html>
`;

// HTTP Server
const server = createServer(async (req, res) => {
  const url = req.url;
  const method = req.method;
  
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  if (url === '/' || url === '/index.html') {
    res.writeHead(200, { 'Content-Type': 'text/html' });
    res.end(htmlTemplate);
  } else if (url === '/api/analyze' && method === 'POST') {
    let body = '';
    req.on('data', chunk => body += chunk.toString());
    req.on('end', async () => {
      try {
        const { address, network } = JSON.parse(body);
        const result = await analyzeToken(address, network);
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify(result));
      } catch (error) {
        res.writeHead(400, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ success: false, error: error.message }));
      }
    });
  } else {
    res.writeHead(404, { 'Content-Type': 'text/plain' });
    res.end('Not Found');
  }
});

server.listen(PORT, () => {
  console.log('🚀 DumpDetector Advanced Token Analyzer Started!');
  console.log('===============================================');
  console.log(`🌐 Web Interface: http://localhost:${PORT}`);
  console.log('===============================================');
  console.log('🔍 Analysis Features:');
  console.log('  ✅ Contract verification (Etherscan/BSCScan)');
  console.log('  ✅ Ownership & holder analysis');
  console.log('  ✅ Security audit verification');
  console.log('  ✅ Risk assessment & scoring');
  console.log('  ✅ Top holders tracking');
  console.log('  ✅ Large seller detection');
  console.log('===============================================');
  console.log('🔑 Using your API keys:');
  console.log('  ✅ Etherscan API: ' + ETHERSCAN_API_KEY.substring(0, 8) + '...');
  console.log('  ✅ BSCScan API: ' + BSCSCAN_API_KEY.substring(0, 8) + '...');
  console.log('===============================================');
  console.log('💡 Try analyzing popular tokens like USDT:');
  console.log('   ******************************************');
});

export default server;
