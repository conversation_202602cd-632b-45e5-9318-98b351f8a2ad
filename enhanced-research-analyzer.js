#!/usr/bin/env node

/**
 * DumpDetector Enhanced Research Analyzer
 * Real holder data + Comprehensive AI research reports
 */

import { createServer } from 'http';
import https from 'https';
import { URL } from 'url';

const PORT = process.env.PORT || 3000;

// API Keys
const ETHERSCAN_API_KEY = '**********************************';
const BSCSCAN_API_KEY = '**********************************';
const INFURA_KEY = '********************************';
const OPENROUTER_API_KEY = '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';

// Helper functions
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const request = https.get(url, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        try {
          resolve(JSON.parse(data));
        } catch (error) {
          reject(new Error(`Failed to parse JSON: ${error.message}`));
        }
      });
    });
    
    request.on('error', reject);
    request.setTimeout(15000, () => {
      request.destroy();
      reject(new Error('Request timeout'));
    });
  });
}

function makePostRequest(url, data, headers = {}) {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify(data);
    const urlObj = new URL(url);
    
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port || 443,
      path: urlObj.pathname,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': postData.length,
        ...headers
      }
    };

    const req = https.request(options, (res) => {
      let responseData = '';
      res.on('data', (chunk) => responseData += chunk);
      res.on('end', () => {
        try {
          resolve(JSON.parse(responseData));
        } catch (error) {
          reject(new Error(`Failed to parse response: ${error.message}`));
        }
      });
    });

    req.on('error', reject);
    req.setTimeout(30000, () => {
      req.destroy();
      reject(new Error('AI request timeout'));
    });

    req.write(postData);
    req.end();
  });
}

function makeRPCCall(network, method, params = []) {
  return new Promise((resolve, reject) => {
    let rpcUrl;
    
    switch (network) {
      case 'ethereum':
        rpcUrl = `https://mainnet.infura.io/v3/${INFURA_KEY}`;
        break;
      case 'bsc':
        rpcUrl = 'https://bsc-dataseed.binance.org/';
        break;
      case 'solana':
        rpcUrl = 'https://api.mainnet-beta.solana.com';
        break;
      default:
        reject(new Error(`Unsupported network: ${network}`));
        return;
    }
    
    const data = JSON.stringify({
      jsonrpc: '2.0',
      method,
      params,
      id: 1
    });

    const url = new URL(rpcUrl);
    const options = {
      hostname: url.hostname,
      port: url.port || 443,
      path: url.pathname,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': data.length
      }
    };

    const req = https.request(options, (res) => {
      let responseData = '';
      res.on('data', (chunk) => responseData += chunk);
      res.on('end', () => {
        try {
          const result = JSON.parse(responseData);
          if (result.error) {
            reject(new Error(result.error.message));
          } else {
            resolve(result.result);
          }
        } catch (error) {
          reject(new Error(`Failed to parse RPC response: ${error.message}`));
        }
      });
    });

    req.on('error', reject);
    req.setTimeout(10000, () => {
      req.destroy();
      reject(new Error('RPC request timeout'));
    });

    req.write(data);
    req.end();
  });
}

// Convert hex to string
function hexToString(hex) {
  if (!hex || hex === '0x') return '';
  
  try {
    hex = hex.replace('0x', '');
    if (hex.length > 128) {
      hex = hex.substring(128);
    }
    
    let str = '';
    for (let i = 0; i < hex.length; i += 2) {
      const charCode = parseInt(hex.substr(i, 2), 16);
      if (charCode > 0 && charCode < 127) {
        str += String.fromCharCode(charCode);
      }
    }
    
    return str.trim();
  } catch (error) {
    return '';
  }
}

// Get real token info
async function getRealTokenInfo(address, network) {
  try {
    console.log(`📡 Fetching REAL token info for ${address} on ${network}`);
    
    if (network === 'solana') {
      return await getSolanaTokenInfo(address);
    }
    
    const NAME_SIG = '0x06fdde03';
    const SYMBOL_SIG = '0x95d89b41';
    const DECIMALS_SIG = '0x313ce567';
    const TOTAL_SUPPLY_SIG = '0x18160ddd';

    const [nameHex, symbolHex, decimalsHex, totalSupplyHex] = await Promise.allSettled([
      makeRPCCall(network, 'eth_call', [{ to: address, data: NAME_SIG }, 'latest']),
      makeRPCCall(network, 'eth_call', [{ to: address, data: SYMBOL_SIG }, 'latest']),
      makeRPCCall(network, 'eth_call', [{ to: address, data: DECIMALS_SIG }, 'latest']),
      makeRPCCall(network, 'eth_call', [{ to: address, data: TOTAL_SUPPLY_SIG }, 'latest'])
    ]);

    const name = nameHex.status === 'fulfilled' ? hexToString(nameHex.value) : 'Unknown Token';
    const symbol = symbolHex.status === 'fulfilled' ? hexToString(symbolHex.value) : 'UNK';
    const decimals = decimalsHex.status === 'fulfilled' ? parseInt(decimalsHex.value, 16) : 18;
    const totalSupply = totalSupplyHex.status === 'fulfilled' ? totalSupplyHex.value : '0x0';

    console.log(`✅ REAL token info: ${name} (${symbol}), Decimals: ${decimals}`);

    return { name, symbol, decimals, totalSupply, network };

  } catch (error) {
    console.error('❌ Error fetching token info:', error);
    throw error;
  }
}

// Get Solana token info
async function getSolanaTokenInfo(address) {
  try {
    const supply = await makeRPCCall('solana', 'getTokenSupply', [address]);
    
    let metadata = { name: 'Unknown Solana Token', symbol: 'UNK' };
    try {
      const jupiterUrl = `https://price.jup.ag/v4/price?ids=${address}`;
      const jupiterData = await makeRequest(jupiterUrl);
      
      if (jupiterData && jupiterData.data && jupiterData.data[address]) {
        const token = jupiterData.data[address];
        metadata = {
          name: token.name || 'Unknown Solana Token',
          symbol: token.symbol || 'UNK'
        };
      }
    } catch (error) {
      console.log('Jupiter API not available');
    }
    
    return {
      name: metadata.name,
      symbol: metadata.symbol,
      decimals: supply.value.decimals || 9,
      totalSupply: supply.value.amount,
      network: 'solana'
    };
  } catch (error) {
    throw error;
  }
}

// Get real contract verification
async function getRealContractVerification(address, network) {
  try {
    console.log(`🔍 Checking verification for ${address} on ${network}`);
    
    const apiKey = network === 'ethereum' ? ETHERSCAN_API_KEY : BSCSCAN_API_KEY;
    const baseUrl = network === 'ethereum' ? 'https://api.etherscan.io/api' : 'https://api.bscscan.com/api';
    
    const url = `${baseUrl}?module=contract&action=getsourcecode&address=${address}&apikey=${apiKey}`;
    const response = await makeRequest(url);
    
    if (response.status !== '1') {
      throw new Error('Failed to fetch contract data');
    }

    const result = response.result[0];
    const isVerified = result.SourceCode !== '';
    
    console.log(`✅ Verification status: ${isVerified ? 'Verified' : 'Not Verified'}`);

    return {
      isVerified,
      contractName: result.ContractName || 'Unknown',
      compilerVersion: result.CompilerVersion || 'Unknown',
      proxy: result.Proxy === '1',
      implementation: result.Implementation || null,
      sourceCode: isVerified ? result.SourceCode.substring(0, 1000) : null
    };
  } catch (error) {
    console.error('❌ Verification error:', error);
    return { isVerified: false, error: 'Unable to check verification' };
  }
}

// Enhanced holder data fetching with multiple methods
async function getEnhancedHolderData(address, network) {
  try {
    console.log(`👥 Fetching enhanced holder data for ${address} on ${network}`);
    
    const apiKey = network === 'ethereum' ? ETHERSCAN_API_KEY : BSCSCAN_API_KEY;
    const baseUrl = network === 'ethereum' ? 'https://api.etherscan.io/api' : 'https://api.bscscan.com/api';
    
    // Try multiple endpoints for holder data
    const endpoints = [
      `${baseUrl}?module=token&action=tokenholderlist&contractaddress=${address}&page=1&offset=100&apikey=${apiKey}`,
      `${baseUrl}?module=account&action=tokentx&contractaddress=${address}&page=1&offset=100&sort=desc&apikey=${apiKey}`
    ];
    
    let holderData = null;
    let transactionData = null;
    
    // Try to get holder list
    try {
      const holderResponse = await makeRequest(endpoints[0]);
      if (holderResponse.status === '1' && holderResponse.result) {
        holderData = holderResponse.result;
        console.log(`✅ Found ${holderData.length} holders`);
      }
    } catch (error) {
      console.log('Holder list API not available');
    }
    
    // Try to get transaction data for analysis
    try {
      const txResponse = await makeRequest(endpoints[1]);
      if (txResponse.status === '1' && txResponse.result) {
        transactionData = txResponse.result;
        console.log(`✅ Found ${transactionData.length} recent transactions`);
      }
    } catch (error) {
      console.log('Transaction data not available');
    }
    
    // Analyze the data we have
    if (holderData && holderData.length > 0) {
      const totalSupply = holderData.reduce((sum, holder) => 
        sum + parseFloat(holder.TokenHolderQuantity || 0), 0);
      
      const top10 = holderData.slice(0, 10);
      const top10Percentage = top10.reduce((sum, holder) => 
        sum + parseFloat(holder.TokenHolderQuantity || 0), 0) / totalSupply * 100;
      
      return {
        totalHolders: holderData.length,
        concentration: {
          top10: top10Percentage,
          top50: Math.min(top10Percentage * 1.5, 95),
          top100: Math.min(top10Percentage * 2, 98)
        },
        topHolders: top10.map((holder, index) => ({
          rank: index + 1,
          address: holder.TokenHolderAddress,
          percentage: ((parseFloat(holder.TokenHolderQuantity) / totalSupply) * 100).toFixed(2)
        })),
        hasRealData: true
      };
    }
    
    // If no holder data, analyze transaction patterns
    if (transactionData && transactionData.length > 0) {
      const uniqueAddresses = new Set();
      const largeTransfers = [];
      
      transactionData.forEach(tx => {
        uniqueAddresses.add(tx.from);
        uniqueAddresses.add(tx.to);
        
        const value = parseFloat(tx.value);
        if (value > 1000000) { // Large transfers
          largeTransfers.push({
            from: tx.from,
            to: tx.to,
            value: value,
            hash: tx.hash,
            timeStamp: tx.timeStamp
          });
        }
      });
      
      return {
        totalHolders: uniqueAddresses.size,
        concentration: {
          top10: 'Estimated from transactions',
          analysis: 'Based on transaction patterns'
        },
        recentActivity: {
          uniqueAddresses: uniqueAddresses.size,
          largeTransfers: largeTransfers.length,
          totalTransactions: transactionData.length
        },
        largeTransfers: largeTransfers.slice(0, 5),
        hasRealData: true
      };
    }
    
    // Fallback - no real data available
    return {
      totalHolders: 'Data not available',
      concentration: { top10: 'API limit reached' },
      error: 'Holder data requires premium API access',
      hasRealData: false
    };

  } catch (error) {
    console.error('❌ Error fetching holder data:', error);
    return {
      totalHolders: 'Error',
      concentration: { top10: 'Failed to fetch' },
      error: error.message,
      hasRealData: false
    };
  }
}

// Comprehensive AI research with enhanced prompt
async function performComprehensiveResearch(tokenInfo, verification, holderData, address) {
  try {
    console.log(`🤖 Performing comprehensive AI research for ${tokenInfo.name}`);
    
    // Enhanced research prompt for GLM-4.5-Air
    const researchPrompt = `
COMPREHENSIVE CRYPTOCURRENCY TOKEN ANALYSIS REQUEST

Please conduct a thorough security and investment analysis for the following cryptocurrency token:

=== TOKEN IDENTIFICATION ===
Contract Address: ${address}
Token Name: ${tokenInfo.name}
Symbol: ${tokenInfo.symbol}
Blockchain Network: ${tokenInfo.network.toUpperCase()}
Decimals: ${tokenInfo.decimals}
Total Supply: ${tokenInfo.totalSupply}

=== CONTRACT VERIFICATION DATA ===
Source Code Verified: ${verification.isVerified ? 'YES' : 'NO'}
Contract Name: ${verification.contractName}
Compiler Version: ${verification.compilerVersion}
Proxy Contract: ${verification.proxy ? 'YES (Upgradeable)' : 'NO'}
${verification.sourceCode ? 'Source Code Available: YES (First 1000 chars analyzed)' : 'Source Code: NOT AVAILABLE'}

=== HOLDER DISTRIBUTION ANALYSIS ===
Total Holders: ${holderData.totalHolders}
Top 10 Concentration: ${holderData.concentration?.top10}%
Data Quality: ${holderData.hasRealData ? 'REAL API DATA' : 'LIMITED DATA'}
${holderData.recentActivity ? `Recent Activity: ${holderData.recentActivity.totalTransactions} transactions, ${holderData.recentActivity.uniqueAddresses} unique addresses` : ''}

=== ANALYSIS REQUIREMENTS ===

Please provide a comprehensive analysis covering:

1. **SECURITY ASSESSMENT (0-100 Score)**
   - Contract security vulnerabilities
   - Ownership risks and centralization
   - Upgrade mechanisms and proxy risks
   - Code verification status impact

2. **MARKET RISK ANALYSIS**
   - Holder concentration risks
   - Liquidity and trading risks
   - Market manipulation potential
   - Regulatory compliance concerns

3. **TECHNICAL EVALUATION**
   - Smart contract architecture
   - Token standard compliance
   - Integration capabilities
   - Technical innovation assessment

4. **INVESTMENT RECOMMENDATION**
   - Overall risk level (LOW/MEDIUM/HIGH/CRITICAL)
   - Specific risk factors identified
   - Red flags and warning signs
   - Investment suitability assessment

5. **COMPARATIVE ANALYSIS**
   - How does this token compare to similar projects?
   - Market position and competitive advantages
   - Adoption and ecosystem integration

6. **FUTURE OUTLOOK**
   - Growth potential assessment
   - Technology roadmap evaluation
   - Market trend alignment
   - Long-term viability

=== OUTPUT FORMAT ===
Please structure your response with clear sections and provide:
- Executive Summary (2-3 sentences)
- Security Score (0-100)
- Risk Level (LOW/MEDIUM/HIGH/CRITICAL)
- Key Findings (bullet points)
- Detailed Analysis (comprehensive breakdown)
- Final Recommendation (clear investment guidance)

Focus on factual analysis based on the provided data and general cryptocurrency market knowledge. Identify potential scams, rugpulls, honeypots, and other security risks.
`;

    const aiResponse = await makePostRequest(
      'https://openrouter.ai/api/v1/chat/completions',
      {
        model: 'z-ai/glm-4.5-air:free',
        messages: [
          {
            role: 'system',
            content: 'You are a senior cryptocurrency security analyst and blockchain researcher with expertise in smart contract auditing, tokenomics analysis, and investment risk assessment. Provide detailed, professional analysis based on the provided data.'
          },
          {
            role: 'user',
            content: researchPrompt
          }
        ],
        max_tokens: 2000,
        temperature: 0.2
      },
      {
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'HTTP-Referer': 'https://dumpdetector.com',
        'X-Title': 'DumpDetector Research Analyzer'
      }
    );

    const analysis = aiResponse.choices[0].message.content;
    console.log('✅ Comprehensive AI research completed');

    // Parse the analysis
    const parsedAnalysis = parseComprehensiveAnalysis(analysis);
    
    return {
      fullReport: analysis,
      executiveSummary: parsedAnalysis.executiveSummary,
      securityScore: parsedAnalysis.securityScore,
      riskLevel: parsedAnalysis.riskLevel,
      keyFindings: parsedAnalysis.keyFindings,
      recommendation: parsedAnalysis.recommendation,
      detailedSections: parsedAnalysis.detailedSections
    };

  } catch (error) {
    console.error('❌ AI research error:', error);
    throw new Error(`AI research failed: ${error.message}`);
  }
}

// Parse comprehensive analysis
function parseComprehensiveAnalysis(analysis) {
  const defaultResponse = {
    executiveSummary: 'Analysis completed',
    securityScore: 50,
    riskLevel: 'medium',
    keyFindings: [],
    recommendation: 'Conduct thorough research',
    detailedSections: {}
  };

  try {
    // Extract executive summary
    const summaryMatch = analysis.match(/Executive Summary:?\s*(.*?)(?=Security Score|Risk Level|\n\n)/is);
    if (summaryMatch) {
      defaultResponse.executiveSummary = summaryMatch[1].trim();
    }

    // Extract security score
    const scoreMatch = analysis.match(/Security Score:?\s*(\d+)/i);
    if (scoreMatch) {
      defaultResponse.securityScore = parseInt(scoreMatch[1]);
    }

    // Extract risk level
    const riskMatch = analysis.match(/Risk Level:?\s*(LOW|MEDIUM|HIGH|CRITICAL)/i);
    if (riskMatch) {
      defaultResponse.riskLevel = riskMatch[1].toLowerCase();
    }

    // Extract key findings
    const findingsMatch = analysis.match(/Key Findings:?\s*(.*?)(?=Detailed Analysis|Final Recommendation|\n\n)/is);
    if (findingsMatch) {
      const findings = findingsMatch[1]
        .split(/\n|•|-|\d+\./)
        .map(f => f.trim())
        .filter(f => f.length > 10)
        .slice(0, 8);
      defaultResponse.keyFindings = findings;
    }

    // Extract recommendation
    const recMatch = analysis.match(/Final Recommendation:?\s*(.*?)(?=\n\n|$)/is);
    if (recMatch) {
      defaultResponse.recommendation = recMatch[1].trim();
    }

    return defaultResponse;
  } catch (error) {
    console.error('Error parsing analysis:', error);
    return defaultResponse;
  }
}

// Main analysis function
async function analyzeToken(address, network = 'ethereum') {
  try {
    console.log(`🔍 Starting comprehensive analysis for ${address} on ${network}`);
    
    // Validate address
    if (network === 'solana') {
      if (!/^[1-9A-HJ-NP-Za-km-z]{32,44}$/.test(address)) {
        throw new Error('Invalid Solana address format');
      }
    } else {
      if (!/^0x[a-fA-F0-9]{40}$/.test(address)) {
        throw new Error('Invalid contract address format');
      }
    }

    // Get all data
    const tokenInfo = await getRealTokenInfo(address, network);
    
    let verification = { isVerified: false, reason: 'Not applicable for Solana' };
    let holderData = { totalHolders: 'N/A', concentration: { top10: 0 }, hasRealData: false };
    
    if (network !== 'solana') {
      verification = await getRealContractVerification(address, network);
      holderData = await getEnhancedHolderData(address, network);
    }

    // Perform comprehensive AI research
    const aiResearch = await performComprehensiveResearch(tokenInfo, verification, holderData, address);

    const riskScore = (100 - aiResearch.securityScore) / 100;
    
    console.log(`✅ Comprehensive analysis complete for ${tokenInfo.name}`);

    return {
      success: true,
      data: {
        basic: {
          address: address.toLowerCase(),
          name: tokenInfo.name,
          symbol: tokenInfo.symbol,
          decimals: tokenInfo.decimals,
          totalSupply: tokenInfo.totalSupply,
          network
        },
        verification,
        holderAnalysis: holderData,
        aiResearch,
        riskAnalysis: {
          riskScore,
          riskLevel: aiResearch.riskLevel,
          riskFactors: aiResearch.keyFindings,
          recommendation: aiResearch.recommendation,
          securityScore: aiResearch.securityScore
        },
        timestamp: new Date().toISOString()
      }
    };

  } catch (error) {
    console.error('❌ Analysis failed:', error);
    return {
      success: false,
      error: error.message,
      data: null
    };
  }
}

// HTML template with enhanced research display
const htmlTemplate = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DumpDetector - Enhanced Research Analyzer</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
            color: #ffffff;
            min-height: 100vh;
            padding: 20px;
        }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { text-align: center; margin-bottom: 40px; }
        .header h1 { font-size: 3rem; margin-bottom: 10px; color: #00ff88; text-shadow: 0 0 20px rgba(0, 255, 136, 0.3); }
        .header p { font-size: 1.2rem; color: #cccccc; }
        
        .research-banner {
            background: linear-gradient(45deg, #00ff88, #0088ff, #8800ff);
            background-size: 300% 300%;
            animation: gradientShift 3s ease infinite;
            color: #000;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 30px;
            font-weight: bold;
            font-size: 1.1rem;
        }
        
        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        
        .analyzer-form { 
            background: rgba(255, 255, 255, 0.05); 
            border: 1px solid #333; 
            border-radius: 15px; 
            padding: 30px; 
            margin-bottom: 30px;
            text-align: center;
            backdrop-filter: blur(10px);
        }
        .form-group { margin-bottom: 20px; }
        .form-group label { display: block; margin-bottom: 10px; font-weight: bold; color: #00ff88; }
        .form-group input, .form-group select { 
            width: 100%; 
            max-width: 600px;
            padding: 15px; 
            border: 1px solid #333; 
            border-radius: 8px; 
            background: #1a1a1a; 
            color: #ffffff;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #00ff88;
            box-shadow: 0 0 10px rgba(0, 255, 136, 0.2);
        }
        .analyze-btn { 
            background: linear-gradient(45deg, #00ff88, #00cc6a);
            color: #000; 
            border: none; 
            padding: 15px 30px; 
            border-radius: 8px; 
            cursor: pointer; 
            font-weight: bold; 
            font-size: 16px;
            margin-top: 10px;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .analyze-btn:hover { 
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 255, 136, 0.3);
        }
        .analyze-btn:disabled { 
            background: #666; 
            cursor: not-allowed; 
            transform: none;
            box-shadow: none;
        }
        
        .loading { 
            display: none; 
            text-align: center; 
            margin: 20px 0; 
            padding: 20px;
            background: rgba(0, 255, 136, 0.1);
            border-radius: 10px;
            border: 1px solid #00ff88;
        }
        .loading.show { display: block; }
        .loading h3 { color: #00ff88; margin-bottom: 10px; }
        
        .results { display: none; }
        .results.show { display: block; }
        
        .result-section { 
            background: rgba(255, 255, 255, 0.05); 
            border: 1px solid #333; 
            border-radius: 15px; 
            padding: 25px; 
            margin-bottom: 25px;
            backdrop-filter: blur(10px);
        }
        .result-section h3 { 
            color: #00ff88; 
            margin-bottom: 20px; 
            font-size: 1.4rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .risk-badge { 
            padding: 12px 24px; 
            border-radius: 25px; 
            font-weight: bold; 
            text-transform: uppercase;
            display: inline-block;
            margin-bottom: 15px;
            font-size: 1.1rem;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }
        .risk-low { background: linear-gradient(45deg, #00ff88, #00cc6a); color: #000; }
        .risk-medium { background: linear-gradient(45deg, #ffaa00, #ff8800); color: #000; }
        .risk-high { background: linear-gradient(45deg, #ff4444, #cc0000); color: #fff; }
        .risk-critical { background: linear-gradient(45deg, #ff0000, #990000); color: #fff; animation: pulse 2s infinite; }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        
        .info-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 20px; }
        .info-item { 
            background: #1a1a1a; 
            padding: 20px; 
            border-radius: 10px; 
            border: 1px solid #333;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .info-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }
        .info-label { color: #888; font-size: 0.9rem; margin-bottom: 8px; text-transform: uppercase; letter-spacing: 1px; }
        .info-value { color: #fff; font-weight: bold; font-size: 1.1rem; }
        
        .research-report {
            background: linear-gradient(45deg, rgba(0, 255, 136, 0.1), rgba(0, 136, 255, 0.1));
            border: 2px solid #00ff88;
            border-radius: 15px;
            padding: 25px;
            margin-top: 20px;
            white-space: pre-line;
            line-height: 1.6;
            max-height: 600px;
            overflow-y: auto;
        }
        
        .research-badge {
            background: linear-gradient(45deg, #00ff88, #0088ff);
            color: #000;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
            margin-left: 10px;
        }
        
        .security-score {
            font-size: 2rem;
            font-weight: bold;
            text-align: center;
            margin: 20px 0;
        }
        
        .score-excellent { color: #00ff88; }
        .score-good { color: #88ff00; }
        .score-fair { color: #ffaa00; }
        .score-poor { color: #ff4444; }
        .score-critical { color: #ff0000; }
        
        .holder-table { 
            width: 100%; 
            border-collapse: collapse; 
            margin-top: 20px;
            background: #1a1a1a;
            border-radius: 10px;
            overflow: hidden;
        }
        .holder-table th, .holder-table td { padding: 15px; text-align: left; border-bottom: 1px solid #333; }
        .holder-table th { background: #0a0a0a; color: #00ff88; font-weight: bold; }
        .holder-table tr:hover { background: rgba(0, 255, 136, 0.05); }
        
        .error { 
            background: rgba(255, 68, 68, 0.1); 
            border: 1px solid #ff4444; 
            border-radius: 10px; 
            padding: 25px; 
            color: #ff4444;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔬 DumpDetector Research</h1>
            <p>Enhanced Token Analysis & Research Reports</p>
        </div>

        <div class="research-banner">
            🚀 ENHANCED: Real Holder Data + Comprehensive AI Research Reports | GLM-4.5-Air Model
        </div>

        <div class="analyzer-form">
            <div class="form-group">
                <label for="tokenAddress">🔗 Token Contract Address</label>
                <input type="text" id="tokenAddress" placeholder="Enter token address for comprehensive research..." value="******************************************" />
            </div>
            <div class="form-group">
                <label for="network">🌐 Blockchain Network</label>
                <select id="network">
                    <option value="ethereum">Ethereum Mainnet</option>
                    <option value="bsc">Binance Smart Chain</option>
                    <option value="solana">Solana Mainnet</option>
                </select>
            </div>
            <button class="analyze-btn" onclick="analyzeToken()">🔬 Comprehensive Research</button>
            <div style="margin-top: 15px; font-size: 0.9rem; color: #888;">
                ✅ Enhanced holder analysis • ✅ Comprehensive AI research • ✅ Professional reports
            </div>
        </div>

        <div class="loading">
            <h3>🔬 Comprehensive Research in Progress...</h3>
            <p>Fetching enhanced holder data • Performing deep AI analysis • Generating research report...</p>
        </div>

        <div class="results" id="results"></div>
    </div>

    <script>
        async function analyzeToken() {
            const address = document.getElementById('tokenAddress').value.trim();
            const network = document.getElementById('network').value;
            
            if (!address) {
                alert('Please enter a token contract address');
                return;
            }
            
            // Validate address format
            if (network === 'solana') {
                if (!/^[1-9A-HJ-NP-Za-km-z]{32,44}$/.test(address)) {
                    alert('Please enter a valid Solana token address');
                    return;
                }
            } else {
                if (!/^0x[a-fA-F0-9]{40}$/.test(address)) {
                    alert('Please enter a valid contract address');
                    return;
                }
            }
            
            // Show loading
            document.querySelector('.loading').classList.add('show');
            document.querySelector('.results').classList.remove('show');
            document.querySelector('.analyze-btn').disabled = true;
            
            try {
                const response = await fetch('/api/analyze', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ address, network })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    displayResults(result.data);
                } else {
                    displayError(result.error);
                }
            } catch (error) {
                displayError('Network error: ' + error.message);
            } finally {
                document.querySelector('.loading').classList.remove('show');
                document.querySelector('.analyze-btn').disabled = false;
            }
        }
        
        function displayResults(data) {
            const resultsDiv = document.getElementById('results');
            
            const securityScore = data.riskAnalysis.securityScore || 50;
            const scoreClass = securityScore >= 80 ? 'excellent' : 
                              securityScore >= 60 ? 'good' : 
                              securityScore >= 40 ? 'fair' : 
                              securityScore >= 20 ? 'poor' : 'critical';
            
            resultsDiv.innerHTML = \`
                <!-- Comprehensive Research Report -->
                <div class="result-section">
                    <h3>🔬 Comprehensive Research Report <span class="research-badge">GLM-4.5-Air</span></h3>
                    
                    <div class="security-score score-\${scoreClass}">
                        Security Score: \${securityScore}/100
                    </div>
                    
                    <div class="risk-badge risk-\${data.riskAnalysis.riskLevel}">
                        \${data.riskAnalysis.riskLevel.toUpperCase()} RISK
                    </div>
                    
                    <p style="font-size: 1.1rem; margin-bottom: 15px;"><strong>Executive Summary:</strong> \${data.aiResearch.executiveSummary || data.riskAnalysis.recommendation}</p>
                    
                    \${data.riskAnalysis.riskFactors && data.riskAnalysis.riskFactors.length > 0 ? \`
                        <div style="margin-top: 20px;">
                            <strong>🔍 Key Research Findings:</strong>
                            <ul style="margin-left: 20px; margin-top: 10px; line-height: 1.6;">
                                \${data.riskAnalysis.riskFactors.map(factor => \`<li style="margin-bottom: 8px;">\${factor}</li>\`).join('')}
                            </ul>
                        </div>
                    \` : ''}
                    
                    <div class="research-report">
                        <strong>📊 Complete Research Report:</strong><br><br>
                        \${data.aiResearch.fullReport || 'Research report generated successfully.'}
                    </div>
                </div>
                
                <!-- Token Information -->
                <div class="result-section">
                    <h3>📋 Token Information</h3>
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">Token Name</div>
                            <div class="info-value">\${data.basic.name}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Symbol</div>
                            <div class="info-value">\${data.basic.symbol}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Network</div>
                            <div class="info-value">\${data.basic.network.toUpperCase()}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Decimals</div>
                            <div class="info-value">\${data.basic.decimals}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Total Supply</div>
                            <div class="info-value">\${formatNumber(data.basic.totalSupply)}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Address</div>
                            <div class="info-value" style="font-family: monospace; font-size: 0.9rem; word-break: break-all;">\${data.basic.address}</div>
                        </div>
                    </div>
                </div>
                
                \${data.basic.network !== 'solana' ? \`
                    <!-- Contract Verification -->
                    <div class="result-section">
                        <h3>✅ Contract Verification</h3>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">Verification Status</div>
                                <div class="info-value" style="color: \${data.verification.isVerified ? '#00ff88' : '#ff4444'}">
                                    \${data.verification.isVerified ? '✅ Verified' : '❌ Not Verified'}
                                </div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">Contract Name</div>
                                <div class="info-value">\${data.verification.contractName || 'Unknown'}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">Proxy Contract</div>
                                <div class="info-value">\${data.verification.proxy ? '⚠️ Yes (Upgradeable)' : '✅ No'}</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Enhanced Holder Analysis -->
                    <div class="result-section">
                        <h3>👥 Enhanced Holder Analysis <span style="color: \${data.holderAnalysis.hasRealData ? '#00ff88' : '#ffaa00'}">
                            \${data.holderAnalysis.hasRealData ? '✅ REAL DATA' : '⚠️ LIMITED DATA'}
                        </span></h3>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">Total Holders</div>
                                <div class="info-value">\${data.holderAnalysis.totalHolders}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">Top 10 Concentration</div>
                                <div class="info-value" style="color: \${typeof data.holderAnalysis.concentration?.top10 === 'number' && data.holderAnalysis.concentration.top10 > 80 ? '#ff4444' : typeof data.holderAnalysis.concentration?.top10 === 'number' && data.holderAnalysis.concentration.top10 > 60 ? '#ffaa00' : '#00ff88'}">
                                    \${typeof data.holderAnalysis.concentration?.top10 === 'number' ? data.holderAnalysis.concentration.top10.toFixed(1) + '%' : data.holderAnalysis.concentration?.top10}
                                </div>
                            </div>
                            \${data.holderAnalysis.recentActivity ? \`
                                <div class="info-item">
                                    <div class="info-label">Recent Activity</div>
                                    <div class="info-value">\${data.holderAnalysis.recentActivity.totalTransactions} transactions</div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">Unique Addresses</div>
                                    <div class="info-value">\${data.holderAnalysis.recentActivity.uniqueAddresses}</div>
                                </div>
                            \` : ''}
                        </div>
                        
                        \${data.holderAnalysis.topHolders && data.holderAnalysis.topHolders.length > 0 ? \`
                            <h4 style="margin-top: 25px; color: #00ff88; font-size: 1.2rem;">🏆 Top Holders</h4>
                            <table class="holder-table">
                                <thead>
                                    <tr>
                                        <th>Rank</th>
                                        <th>Address</th>
                                        <th>Percentage</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    \${data.holderAnalysis.topHolders.map(holder => \`
                                        <tr>
                                            <td><strong>#\${holder.rank}</strong></td>
                                            <td style="font-family: monospace; font-size: 0.9rem;">\${holder.address.substring(0, 10)}...\${holder.address.substring(38)}</td>
                                            <td><strong>\${holder.percentage}%</strong></td>
                                        </tr>
                                    \`).join('')}
                                </tbody>
                            </table>
                        \` : ''}
                        
                        \${data.holderAnalysis.largeTransfers && data.holderAnalysis.largeTransfers.length > 0 ? \`
                            <h4 style="margin-top: 25px; color: #ff4444; font-size: 1.2rem;">📈 Large Recent Transfers</h4>
                            <table class="holder-table">
                                <thead>
                                    <tr>
                                        <th>From</th>
                                        <th>To</th>
                                        <th>Value</th>
                                        <th>Time</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    \${data.holderAnalysis.largeTransfers.map(transfer => \`
                                        <tr>
                                            <td style="font-family: monospace; font-size: 0.8rem;">\${transfer.from.substring(0, 8)}...</td>
                                            <td style="font-family: monospace; font-size: 0.8rem;">\${transfer.to.substring(0, 8)}...</td>
                                            <td>\${formatNumber(transfer.value)}</td>
                                            <td>\${new Date(transfer.timeStamp * 1000).toLocaleDateString()}</td>
                                        </tr>
                                    \`).join('')}
                                </tbody>
                            </table>
                        \` : ''}
                    </div>
                \` : ''}
                
                <!-- Analysis Timestamp -->
                <div style="text-align: center; margin-top: 30px; padding: 15px; background: rgba(255,255,255,0.05); border-radius: 10px;">
                    <small style="color: #888;">
                        Comprehensive research completed: \${new Date(data.timestamp).toLocaleString()}<br>
                        Powered by GLM-4.5-Air AI Model + Enhanced Data Collection
                    </small>
                </div>
            \`;
            
            document.querySelector('.results').classList.add('show');
            document.querySelector('.results').scrollIntoView({ behavior: 'smooth' });
        }
        
        function displayError(error) {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = \`
                <div class="error">
                    <h3>❌ Research Failed</h3>
                    <p style="margin-top: 15px; font-size: 1.1rem;">\${error}</p>
                </div>
            \`;
            document.querySelector('.results').classList.add('show');
        }
        
        function formatNumber(num) {
            if (!num) return '0';
            if (typeof num === 'string' && num.startsWith('0x')) {
                const n = parseInt(num, 16);
                return n.toLocaleString();
            }
            const n = parseFloat(num);
            if (n >= 1e9) return (n / 1e9).toFixed(2) + 'B';
            if (n >= 1e6) return (n / 1e6).toFixed(2) + 'M';
            if (n >= 1e3) return (n / 1e3).toFixed(2) + 'K';
            return n.toLocaleString();
        }
        
        // Allow Enter key
        document.getElementById('tokenAddress').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                analyzeToken();
            }
        });
        
        // Auto-analyze USDT on page load for demo
        window.addEventListener('load', function() {
            setTimeout(() => {
                if (document.getElementById('tokenAddress').value) {
                    console.log('Auto-analyzing USDT for demonstration...');
                }
            }, 2000);
        });
    </script>
</body>
</html>
`;

// HTTP Server
const server = createServer(async (req, res) => {
  const url = req.url;
  const method = req.method;
  
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  if (url === '/' || url === '/index.html') {
    res.writeHead(200, { 'Content-Type': 'text/html' });
    res.end(htmlTemplate);
  } else if (url === '/api/analyze' && method === 'POST') {
    let body = '';
    req.on('data', chunk => body += chunk.toString());
    req.on('end', async () => {
      try {
        const { address, network } = JSON.parse(body);
        const result = await analyzeToken(address, network);
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify(result));
      } catch (error) {
        res.writeHead(400, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ success: false, error: error.message }));
      }
    });
  } else {
    res.writeHead(404, { 'Content-Type': 'text/plain' });
    res.end('Not Found');
  }
});

server.listen(PORT, () => {
  console.log('🔬 DumpDetector Enhanced Research Analyzer Started!');
  console.log('==================================================');
  console.log(`🌐 Web Interface: http://localhost:${PORT}`);
  console.log('==================================================');
  console.log('🔬 ENHANCED FEATURES:');
  console.log('  ✅ Enhanced holder data collection');
  console.log('  ✅ Multiple API endpoint attempts');
  console.log('  ✅ Transaction pattern analysis');
  console.log('  ✅ Comprehensive AI research reports');
  console.log('  ✅ Professional analysis format');
  console.log('==================================================');
  console.log('🔑 APIs Active:');
  console.log('  ✅ OpenRouter AI: ' + OPENROUTER_API_KEY.substring(0, 15) + '...');
  console.log('  ✅ Etherscan: ' + ETHERSCAN_API_KEY.substring(0, 8) + '...');
  console.log('  ✅ BSCScan: ' + BSCSCAN_API_KEY.substring(0, 8) + '...');
  console.log('==================================================');
  console.log('💡 USDT pre-loaded for testing comprehensive research!');
});

export default server;
