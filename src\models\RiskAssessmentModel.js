/**
 * AI Risk Assessment Model for DumpDetector
 * Calculates overall risk scores based on multiple analysis factors
 */

import { Logger } from '../utils/Logger.js';

export class RiskAssessmentModel {
  constructor({ database, redis, config }) {
    this.database = database;
    this.redis = redis;
    this.config = config;
    this.logger = new Logger('RiskAssessmentModel');
    
    this.weights = config.get('risk.weights') || {
      contractRisk: 0.4,
      socialRisk: 0.3,
      marketRisk: 0.2,
      liquidityRisk: 0.1
    };
  }

  async assessRisk(tokenData, analyses) {
    this.logger.info(`🤖 Assessing risk for: ${tokenData.address}`);
    
    const contractAnalysis = analyses.contract || {};
    const socialAnalysis = analyses.social || {};
    const marketAnalysis = analyses.market || {};
    
    // Calculate weighted risk score
    const contractRisk = contractAnalysis.riskScore || 0.5;
    const socialRisk = socialAnalysis.riskScore || 0.3;
    const marketRisk = marketAnalysis.riskScore || 0.3;
    const liquidityRisk = this.calculateLiquidityRisk(tokenData);
    
    const overallRisk = (
      contractRisk * this.weights.contractRisk +
      socialRisk * this.weights.socialRisk +
      marketRisk * this.weights.marketRisk +
      liquidityRisk * this.weights.liquidityRisk
    );
    
    const riskLevel = this.getRiskLevel(overallRisk);
    
    return {
      riskScore: overallRisk,
      riskLevel,
      components: {
        contract: contractRisk,
        social: socialRisk,
        market: marketRisk,
        liquidity: liquidityRisk
      },
      confidence: 0.8,
      timestamp: new Date().toISOString()
    };
  }

  calculateLiquidityRisk(tokenData) {
    const liquidity = tokenData.liquidity?.totalLiquidity || 0;
    
    if (liquidity < 1000) return 0.9;
    if (liquidity < 10000) return 0.7;
    if (liquidity < 50000) return 0.5;
    if (liquidity < 100000) return 0.3;
    return 0.1;
  }

  getRiskLevel(riskScore) {
    const thresholds = this.config.getRiskThresholds();
    
    if (riskScore >= thresholds.high) return 'critical';
    if (riskScore >= thresholds.medium) return 'high';
    if (riskScore >= thresholds.low) return 'medium';
    return 'low';
  }
}
