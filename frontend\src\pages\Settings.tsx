import React from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  <PERSON>,
  Card<PERSON>ontent,
  Switch,
  FormControlLabel,
  <PERSON>,
  Di<PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
} from '@mui/material';
import {
  Settings as SettingsIcon,
  Notifications,
  Security,
  Telegram,
  Email,
} from '@mui/icons-material';

const Settings: React.FC = () => {
  return (
    <Box sx={{ flexGrow: 1 }}>
      <Typography variant="h4" sx={{ mb: 3, fontWeight: 700 }}>
        ⚙️ Settings
      </Typography>

      {/* Notification Settings */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Notifications sx={{ color: '#00ff88', mr: 1 }} />
            <Typography variant="h6">
              Notification Preferences
            </Typography>
          </Box>
          
          <Box sx={{ mb: 2 }}>
            <FormControlLabel
              control={<Switch defaultChecked />}
              label="Enable Browser Notifications"
            />
            <Typography variant="body2" sx={{ color: '#cccccc', ml: 4 }}>
              Receive instant alerts in your browser when high-risk tokens are detected
            </Typography>
          </Box>

          <Box sx={{ mb: 2 }}>
            <FormControlLabel
              control={<Switch defaultChecked />}
              label="Telegram Notifications"
            />
            <Typography variant="body2" sx={{ color: '#cccccc', ml: 4 }}>
              Get alerts via Telegram bot (requires bot setup)
            </Typography>
          </Box>

          <Box sx={{ mb: 2 }}>
            <FormControlLabel
              control={<Switch />}
              label="Email Notifications"
            />
            <Typography variant="body2" sx={{ color: '#cccccc', ml: 4 }}>
              Receive daily summary reports via email
            </Typography>
          </Box>
        </CardContent>
      </Card>

      {/* Risk Threshold Settings */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Security sx={{ color: '#ffaa00', mr: 1 }} />
            <Typography variant="h6">
              Alert Thresholds
            </Typography>
          </Box>
          
          <Typography variant="body2" sx={{ color: '#cccccc', mb: 2 }}>
            Configure when you want to receive alerts based on risk levels
          </Typography>

          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
            <Chip
              label="Critical Risk (80-100%)"
              sx={{ backgroundColor: '#ff0000', color: '#ffffff' }}
            />
            <Chip
              label="High Risk (60-80%)"
              sx={{ backgroundColor: '#ff4444', color: '#ffffff' }}
            />
            <Chip
              label="Medium Risk (40-60%)"
              sx={{ backgroundColor: '#ffaa00', color: '#000000' }}
            />
            <Chip
              label="Low Risk (0-40%)"
              sx={{ backgroundColor: '#00ff88', color: '#000000' }}
            />
          </Box>

          <Box sx={{ mb: 2 }}>
            <FormControlLabel
              control={<Switch defaultChecked />}
              label="Alert on Critical Risk"
            />
          </Box>

          <Box sx={{ mb: 2 }}>
            <FormControlLabel
              control={<Switch defaultChecked />}
              label="Alert on High Risk"
            />
          </Box>

          <Box sx={{ mb: 2 }}>
            <FormControlLabel
              control={<Switch />}
              label="Alert on Medium Risk"
            />
          </Box>

          <Box sx={{ mb: 2 }}>
            <FormControlLabel
              control={<Switch />}
              label="Alert on Low Risk"
            />
          </Box>
        </CardContent>
      </Card>

      {/* Network Settings */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2 }}>
            Monitored Networks
          </Typography>
          
          <Typography variant="body2" sx={{ color: '#cccccc', mb: 2 }}>
            Select which blockchain networks to monitor for new tokens
          </Typography>

          <Box sx={{ mb: 2 }}>
            <FormControlLabel
              control={<Switch defaultChecked />}
              label="Ethereum"
            />
          </Box>

          <Box sx={{ mb: 2 }}>
            <FormControlLabel
              control={<Switch defaultChecked />}
              label="Binance Smart Chain (BSC)"
            />
          </Box>

          <Box sx={{ mb: 2 }}>
            <FormControlLabel
              control={<Switch defaultChecked />}
              label="Polygon"
            />
          </Box>

          <Box sx={{ mb: 2 }}>
            <FormControlLabel
              control={<Switch />}
              label="Avalanche"
            />
          </Box>

          <Box sx={{ mb: 2 }}>
            <FormControlLabel
              control={<Switch />}
              label="Solana"
            />
          </Box>
        </CardContent>
      </Card>

      {/* Telegram Bot Setup */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Telegram sx={{ color: '#0088cc', mr: 1 }} />
            <Typography variant="h6">
              Telegram Bot Setup
            </Typography>
          </Box>
          
          <Alert severity="info" sx={{ mb: 2 }}>
            To receive Telegram notifications, start a chat with our bot and subscribe to alerts.
          </Alert>

          <Typography variant="body2" sx={{ color: '#cccccc', mb: 2 }}>
            <strong>Steps to setup Telegram alerts:</strong>
          </Typography>
          
          <Box sx={{ ml: 2, mb: 2 }}>
            <Typography variant="body2" sx={{ color: '#cccccc', mb: 1 }}>
              1. Open Telegram and search for <strong>@DumpDetectorBot</strong>
            </Typography>
            <Typography variant="body2" sx={{ color: '#cccccc', mb: 1 }}>
              2. Start a chat and send <strong>/start</strong>
            </Typography>
            <Typography variant="body2" sx={{ color: '#cccccc', mb: 1 }}>
              3. Send <strong>/subscribe</strong> to enable alerts
            </Typography>
            <Typography variant="body2" sx={{ color: '#cccccc', mb: 1 }}>
              4. Configure your preferences with <strong>/settings</strong>
            </Typography>
          </Box>

          <Button
            variant="outlined"
            startIcon={<Telegram />}
            href="https://t.me/DumpDetectorBot"
            target="_blank"
            sx={{ color: '#0088cc', borderColor: '#0088cc' }}
          >
            Open Telegram Bot
          </Button>
        </CardContent>
      </Card>

      {/* API Access */}
      <Card>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2 }}>
            API Access
          </Typography>
          
          <Typography variant="body2" sx={{ color: '#cccccc', mb: 2 }}>
            Access DumpDetector data programmatically via our REST API
          </Typography>

          <Alert severity="warning" sx={{ mb: 2 }}>
            API access is currently in beta. Contact support for API keys.
          </Alert>

          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button variant="outlined">
              Request API Key
            </Button>
            <Button variant="outlined">
              View Documentation
            </Button>
          </Box>
        </CardContent>
      </Card>
    </Box>
  );
};

export default Settings;
