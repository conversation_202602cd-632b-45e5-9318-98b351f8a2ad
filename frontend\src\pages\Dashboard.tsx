import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  Typography,
  Box,
  Chip,
  LinearProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  TrendingUp,
  Security,
  Warning,
  Error,
  Visibility,
  OpenInNew,
} from '@mui/icons-material';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, ResponsiveContainer, Pie<PERSON>hart, Pie, Cell } from 'recharts';
import { useSocket } from '../contexts/SocketContext';
import { Token, Alert } from '../types';
import { format } from 'date-fns';

const Dashboard: React.FC = () => {
  const { connected, latestTokens, latestAlerts, systemStats } = useSocket();
  const [recentTokens, setRecentTokens] = useState<Token[]>([]);
  const [recentAlerts, setRecentAlerts] = useState<Alert[]>([]);

  useEffect(() => {
    // Update recent tokens and alerts from socket data
    setRecentTokens(latestTokens.slice(0, 10));
    setRecentAlerts(latestAlerts.slice(0, 5));
  }, [latestTokens, latestAlerts]);

  const getRiskColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'low': return '#00ff88';
      case 'medium': return '#ffaa00';
      case 'high': return '#ff4444';
      case 'critical': return '#ff0000';
      default: return '#cccccc';
    }
  };

  const getRiskIcon = (riskLevel: string) => {
    switch (riskLevel) {
      case 'low': return <Security sx={{ color: '#00ff88' }} />;
      case 'medium': return <Warning sx={{ color: '#ffaa00' }} />;
      case 'high': return <Error sx={{ color: '#ff4444' }} />;
      case 'critical': return <Error sx={{ color: '#ff0000' }} />;
      default: return <Security sx={{ color: '#cccccc' }} />;
    }
  };

  // Mock data for charts
  const detectionData = [
    { time: '00:00', detections: 12 },
    { time: '04:00', detections: 8 },
    { time: '08:00', detections: 15 },
    { time: '12:00', detections: 23 },
    { time: '16:00', detections: 18 },
    { time: '20:00', detections: 14 },
  ];

  const riskDistribution = [
    { name: 'Low Risk', value: 65, color: '#00ff88' },
    { name: 'Medium Risk', value: 20, color: '#ffaa00' },
    { name: 'High Risk', value: 12, color: '#ff4444' },
    { name: 'Critical Risk', value: 3, color: '#ff0000' },
  ];

  return (
    <Box sx={{ flexGrow: 1 }}>
      <Typography variant="h4" sx={{ mb: 3, fontWeight: 700 }}>
        🚨 DumpDetector Dashboard
      </Typography>

      {/* Status Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <TrendingUp sx={{ color: '#00ff88', mr: 1 }} />
                <Typography variant="h6">Tokens Monitored</Typography>
              </Box>
              <Typography variant="h3" sx={{ color: '#00ff88' }}>
                {systemStats?.tokensMonitored.toLocaleString() || '1,250'}
              </Typography>
              <Typography variant="body2" sx={{ color: '#cccccc' }}>
                Real-time scanning
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Warning sx={{ color: '#ffaa00', mr: 1 }} />
                <Typography variant="h6">Alerts (24h)</Typography>
              </Box>
              <Typography variant="h3" sx={{ color: '#ffaa00' }}>
                {systemStats?.alertsSent24h || '45'}
              </Typography>
              <Typography variant="body2" sx={{ color: '#cccccc' }}>
                High-risk detections
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Security sx={{ color: '#00ff88', mr: 1 }} />
                <Typography variant="h6">Detection Rate</Typography>
              </Box>
              <Typography variant="h3" sx={{ color: '#00ff88' }}>
                {systemStats?.detectionRate || '94.2'}%
              </Typography>
              <Typography variant="body2" sx={{ color: '#cccccc' }}>
                Accuracy rate
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Error sx={{ color: '#ff4444', mr: 1 }} />
                <Typography variant="h6">System Status</Typography>
              </Box>
              <Chip
                label={connected ? 'Online' : 'Offline'}
                color={connected ? 'success' : 'error'}
                sx={{ fontSize: '1rem', fontWeight: 600 }}
              />
              <Typography variant="body2" sx={{ color: '#cccccc', mt: 1 }}>
                {connected ? 'All systems operational' : 'Connection issues'}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Charts */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                Detection Activity (24h)
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={detectionData}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#333" />
                  <XAxis dataKey="time" stroke="#cccccc" />
                  <YAxis stroke="#cccccc" />
                  <RechartsTooltip 
                    contentStyle={{ 
                      backgroundColor: '#1a1a1a', 
                      border: '1px solid #333',
                      color: '#ffffff'
                    }} 
                  />
                  <Line 
                    type="monotone" 
                    dataKey="detections" 
                    stroke="#00ff88" 
                    strokeWidth={2}
                    dot={{ fill: '#00ff88', strokeWidth: 2, r: 4 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                Risk Distribution
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={riskDistribution}
                    cx="50%"
                    cy="50%"
                    innerRadius={60}
                    outerRadius={100}
                    paddingAngle={5}
                    dataKey="value"
                  >
                    {riskDistribution.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <RechartsTooltip 
                    contentStyle={{ 
                      backgroundColor: '#1a1a1a', 
                      border: '1px solid #333',
                      color: '#ffffff'
                    }} 
                  />
                </PieChart>
              </ResponsiveContainer>
              <Box sx={{ mt: 2 }}>
                {riskDistribution.map((item, index) => (
                  <Box key={index} sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <Box 
                      sx={{ 
                        width: 12, 
                        height: 12, 
                        backgroundColor: item.color, 
                        mr: 1,
                        borderRadius: '50%'
                      }} 
                    />
                    <Typography variant="body2" sx={{ color: '#cccccc' }}>
                      {item.name}: {item.value}%
                    </Typography>
                  </Box>
                ))}
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Recent Activity */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={7}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                Recently Detected Tokens
              </Typography>
              <TableContainer>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>Token</TableCell>
                      <TableCell>Network</TableCell>
                      <TableCell>Risk</TableCell>
                      <TableCell>Detected</TableCell>
                      <TableCell>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {recentTokens.map((token) => (
                      <TableRow key={token._id}>
                        <TableCell>
                          <Box>
                            <Typography variant="body2" sx={{ fontWeight: 600 }}>
                              {token.symbol}
                            </Typography>
                            <Typography variant="caption" sx={{ color: '#cccccc' }}>
                              {token.name}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Chip 
                            label={token.network.toUpperCase()} 
                            size="small" 
                            variant="outlined"
                          />
                        </TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            {getRiskIcon(token.riskLevel)}
                            <Typography 
                              variant="body2" 
                              sx={{ 
                                ml: 1, 
                                color: getRiskColor(token.riskLevel),
                                fontWeight: 600
                              }}
                            >
                              {(token.riskScore * 100).toFixed(1)}%
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Typography variant="caption" sx={{ color: '#cccccc' }}>
                            {format(new Date(token.createdAt), 'HH:mm:ss')}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Tooltip title="View Details">
                            <IconButton size="small" sx={{ color: '#00ff88' }}>
                              <Visibility />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="View on Explorer">
                            <IconButton size="small" sx={{ color: '#cccccc' }}>
                              <OpenInNew />
                            </IconButton>
                          </Tooltip>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={5}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                Recent Alerts
              </Typography>
              <Box sx={{ maxHeight: 400, overflow: 'auto' }}>
                {recentAlerts.map((alert) => (
                  <Box 
                    key={alert._id} 
                    sx={{ 
                      p: 2, 
                      mb: 2, 
                      border: '1px solid #333', 
                      borderRadius: 1,
                      borderLeft: `4px solid ${getRiskColor(alert.riskLevel)}`
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      {getRiskIcon(alert.riskLevel)}
                      <Typography variant="body2" sx={{ ml: 1, fontWeight: 600 }}>
                        {alert.alertType.replace('_', ' ').toUpperCase()}
                      </Typography>
                    </Box>
                    <Typography variant="body2" sx={{ color: '#cccccc', mb: 1 }}>
                      {alert.tokenName} ({alert.tokenSymbol})
                    </Typography>
                    <Typography variant="caption" sx={{ color: '#888' }}>
                      {format(new Date(alert.createdAt), 'HH:mm:ss')} - Risk: {(alert.riskScore * 100).toFixed(1)}%
                    </Typography>
                  </Box>
                ))}
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Dashboard;
