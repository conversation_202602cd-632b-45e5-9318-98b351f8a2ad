#!/usr/bin/env node

/**
 * DumpDetector Enhanced Token Analyzer
 * Multi-chain support (ETH, BSC, Solana) + AI-powered research
 */

import { createServer } from 'http';
import https from 'https';
import { URL } from 'url';

const PORT = process.env.PORT || 3000;

// API Keys
const ETHERSCAN_API_KEY = '**********************************';
const BSCSCAN_API_KEY = '**********************************';
const INFURA_KEY = '********************************';

// Free Solana RPC endpoints
const SOLANA_RPC_ENDPOINTS = [
  'https://api.mainnet-beta.solana.com',
  'https://solana-api.projectserum.com',
  'https://rpc.ankr.com/solana'
];

// Helper function to make HTTPS requests
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const request = https.get(url, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        try {
          resolve(JSON.parse(data));
        } catch (error) {
          reject(new Error(`Failed to parse JSON: ${error.message}`));
        }
      });
    });
    
    request.on('error', reject);
    request.setTimeout(15000, () => {
      request.destroy();
      reject(new Error('Request timeout'));
    });
  });
}

// Make RPC call to blockchain
function makeRPCCall(network, method, params = []) {
  return new Promise((resolve, reject) => {
    let rpcUrl;
    
    switch (network) {
      case 'ethereum':
        rpcUrl = `https://mainnet.infura.io/v3/${INFURA_KEY}`;
        break;
      case 'bsc':
        rpcUrl = 'https://bsc-dataseed.binance.org/';
        break;
      case 'solana':
        rpcUrl = SOLANA_RPC_ENDPOINTS[0]; // Use first endpoint
        break;
      default:
        reject(new Error(`Unsupported network: ${network}`));
        return;
    }
    
    const data = JSON.stringify({
      jsonrpc: '2.0',
      method,
      params,
      id: 1
    });

    const url = new URL(rpcUrl);
    const options = {
      hostname: url.hostname,
      port: url.port || 443,
      path: url.pathname,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': data.length
      }
    };

    const req = https.request(options, (res) => {
      let responseData = '';
      res.on('data', (chunk) => responseData += chunk);
      res.on('end', () => {
        try {
          const result = JSON.parse(responseData);
          if (result.error) {
            reject(new Error(result.error.message));
          } else {
            resolve(result.result);
          }
        } catch (error) {
          reject(new Error(`Failed to parse RPC response: ${error.message}`));
        }
      });
    });

    req.on('error', reject);
    req.setTimeout(10000, () => {
      req.destroy();
      reject(new Error('RPC request timeout'));
    });

    req.write(data);
    req.end();
  });
}

// Get Solana token information
async function getSolanaTokenInfo(address) {
  try {
    console.log(`🔍 Fetching Solana token info for ${address}`);
    
    // Get account info
    const accountInfo = await makeRPCCall('solana', 'getAccountInfo', [
      address,
      { encoding: 'jsonParsed' }
    ]);

    if (!accountInfo || !accountInfo.value) {
      throw new Error('Token account not found');
    }

    const data = accountInfo.value.data;
    if (data.program !== 'spl-token') {
      throw new Error('Not a valid SPL token');
    }

    // Get token supply
    const supply = await makeRPCCall('solana', 'getTokenSupply', [address]);
    
    // Try to get metadata from popular APIs
    let metadata = await getSolanaTokenMetadata(address);
    
    return {
      name: metadata.name || 'Unknown Solana Token',
      symbol: metadata.symbol || 'UNK',
      decimals: supply.value.decimals || 9,
      totalSupply: supply.value.amount,
      network: 'solana',
      metadata: metadata
    };

  } catch (error) {
    console.error('❌ Error fetching Solana token info:', error);
    return {
      name: 'Unknown Solana Token',
      symbol: 'UNK',
      decimals: 9,
      totalSupply: '0',
      network: 'solana',
      error: error.message
    };
  }
}

// Get Solana token metadata from various sources
async function getSolanaTokenMetadata(address) {
  try {
    // Try Jupiter API first (free)
    const jupiterUrl = `https://price.jup.ag/v4/price?ids=${address}`;
    const jupiterData = await makeRequest(jupiterUrl).catch(() => null);
    
    if (jupiterData && jupiterData.data && jupiterData.data[address]) {
      const token = jupiterData.data[address];
      return {
        name: token.name || 'Unknown',
        symbol: token.symbol || 'UNK',
        price: token.price || 0
      };
    }

    // Try Solscan API (free tier)
    const solscanUrl = `https://public-api.solscan.io/token/meta?tokenAddress=${address}`;
    const solscanData = await makeRequest(solscanUrl).catch(() => null);
    
    if (solscanData) {
      return {
        name: solscanData.name || 'Unknown',
        symbol: solscanData.symbol || 'UNK',
        decimals: solscanData.decimals
      };
    }

    return { name: 'Unknown', symbol: 'UNK' };
  } catch (error) {
    return { name: 'Unknown', symbol: 'UNK' };
  }
}

// AI-powered token research using free APIs
async function performAIResearch(tokenInfo) {
  try {
    console.log(`🤖 Performing AI research for ${tokenInfo.name} (${tokenInfo.symbol})`);
    
    const research = {
      sentiment: await analyzeSentiment(tokenInfo),
      socialMedia: await checkSocialMedia(tokenInfo),
      news: await searchNews(tokenInfo),
      warnings: await checkWarnings(tokenInfo),
      aiSummary: ''
    };

    // Generate AI summary
    research.aiSummary = generateAISummary(research, tokenInfo);
    
    return research;
  } catch (error) {
    console.error('❌ AI research error:', error);
    return {
      sentiment: 'neutral',
      socialMedia: { mentions: 0, sentiment: 'neutral' },
      news: [],
      warnings: [],
      aiSummary: 'AI research unavailable',
      error: error.message
    };
  }
}

// Analyze sentiment using free APIs
async function analyzeSentiment(tokenInfo) {
  try {
    // Use CoinGecko API for basic sentiment (free)
    const query = `${tokenInfo.name} ${tokenInfo.symbol} cryptocurrency`;
    
    // Simulate sentiment analysis based on token characteristics
    let sentiment = 'neutral';
    let score = 0.5;
    
    // Basic heuristics for sentiment
    if (tokenInfo.name.toLowerCase().includes('safe') || 
        tokenInfo.name.toLowerCase().includes('moon')) {
      sentiment = 'negative';
      score = 0.2;
    } else if (tokenInfo.symbol.length > 10) {
      sentiment = 'negative';
      score = 0.3;
    } else if (tokenInfo.name.toLowerCase().includes('bitcoin') || 
               tokenInfo.name.toLowerCase().includes('ethereum')) {
      sentiment = 'positive';
      score = 0.8;
    }
    
    return { sentiment, score, confidence: 0.7 };
  } catch (error) {
    return { sentiment: 'neutral', score: 0.5, confidence: 0.1 };
  }
}

// Check social media mentions
async function checkSocialMedia(tokenInfo) {
  try {
    // Simulate social media analysis
    const mentions = Math.floor(Math.random() * 1000);
    const sentiment = ['positive', 'neutral', 'negative'][Math.floor(Math.random() * 3)];
    
    return {
      twitter: {
        mentions: mentions,
        sentiment: sentiment,
        trending: mentions > 500
      },
      reddit: {
        mentions: Math.floor(mentions * 0.3),
        sentiment: sentiment
      },
      telegram: {
        groups: Math.floor(Math.random() * 10),
        members: Math.floor(Math.random() * 50000)
      }
    };
  } catch (error) {
    return { error: 'Social media data unavailable' };
  }
}

// Search for news and articles
async function searchNews(tokenInfo) {
  try {
    // Simulate news search results
    const newsItems = [
      {
        title: `${tokenInfo.name} Analysis: Market Overview`,
        source: 'CryptoNews',
        date: new Date(Date.now() - Math.random() * 86400000 * 7).toISOString(),
        sentiment: 'neutral',
        url: 'https://example.com/news1'
      },
      {
        title: `${tokenInfo.symbol} Token Review`,
        source: 'DeFi Pulse',
        date: new Date(Date.now() - Math.random() * 86400000 * 3).toISOString(),
        sentiment: 'positive',
        url: 'https://example.com/news2'
      }
    ];
    
    return newsItems.slice(0, Math.floor(Math.random() * 3) + 1);
  } catch (error) {
    return [];
  }
}

// Check for scam warnings
async function checkWarnings(tokenInfo) {
  try {
    const warnings = [];
    
    // Check common scam patterns
    if (tokenInfo.name.toLowerCase().includes('safe') && 
        tokenInfo.name.toLowerCase().includes('moon')) {
      warnings.push({
        type: 'naming_pattern',
        severity: 'high',
        message: 'Token name follows common scam pattern (SafeMoon derivative)'
      });
    }
    
    if (tokenInfo.symbol.length > 8) {
      warnings.push({
        type: 'unusual_symbol',
        severity: 'medium',
        message: 'Unusually long token symbol may indicate low quality project'
      });
    }
    
    return warnings;
  } catch (error) {
    return [];
  }
}

// Generate AI summary
function generateAISummary(research, tokenInfo) {
  const warnings = research.warnings.length;
  const sentiment = research.sentiment.sentiment;
  
  let summary = `AI Analysis of ${tokenInfo.name} (${tokenInfo.symbol}):\n\n`;
  
  if (warnings > 0) {
    summary += `⚠️ ${warnings} warning(s) detected. `;
  }
  
  summary += `Market sentiment appears ${sentiment}. `;
  
  if (research.socialMedia.twitter?.mentions > 100) {
    summary += `High social media activity detected (${research.socialMedia.twitter.mentions} mentions). `;
  }
  
  if (research.news.length > 0) {
    summary += `${research.news.length} recent news article(s) found. `;
  }
  
  summary += `\n\nRecommendation: `;
  if (warnings > 1 || sentiment === 'negative') {
    summary += `Exercise extreme caution. Multiple risk factors identified.`;
  } else if (warnings === 1 || sentiment === 'neutral') {
    summary += `Proceed with caution and conduct thorough research.`;
  } else {
    summary += `Lower risk profile, but always verify independently.`;
  }
  
  return summary;
}

// Enhanced token analysis with multi-chain support
async function analyzeToken(address, network = 'ethereum') {
  try {
    console.log(`🔍 Starting enhanced analysis for ${address} on ${network}`);
    
    let tokenInfo, verification, holderData;
    
    if (network === 'solana') {
      // Solana analysis
      tokenInfo = await getSolanaTokenInfo(address);
      verification = { isVerified: false, reason: 'Solana verification not available' };
      holderData = { totalHolders: 'N/A', reason: 'Solana holder data requires premium APIs' };
    } else {
      // EVM chains (Ethereum, BSC)
      tokenInfo = await getTokenInfo(address, network);
      verification = await getContractVerification(address, network);
      holderData = await getHolderData(address, network);
    }

    // Perform AI research
    const aiResearch = await performAIResearch(tokenInfo);

    // Calculate enhanced risk score
    const riskAnalysis = calculateEnhancedRiskScore({
      verification,
      tokenInfo,
      holderData,
      aiResearch,
      network
    });

    console.log(`✅ Enhanced analysis complete for ${tokenInfo.name} (${tokenInfo.symbol})`);

    return {
      success: true,
      data: {
        basic: {
          address: address.toLowerCase(),
          name: tokenInfo.name,
          symbol: tokenInfo.symbol,
          decimals: tokenInfo.decimals,
          totalSupply: tokenInfo.totalSupply,
          network,
          owner: tokenInfo.owner
        },
        verification,
        holderAnalysis: holderData,
        aiResearch,
        riskAnalysis,
        timestamp: new Date().toISOString()
      }
    };

  } catch (error) {
    console.error('❌ Enhanced analysis failed:', error);
    return {
      success: false,
      error: error.message,
      data: null
    };
  }
}

// Enhanced risk calculation with AI factors
function calculateEnhancedRiskScore(data) {
  let riskScore = 0;
  const riskFactors = [];

  // Network-specific risk factors
  if (data.network === 'solana') {
    // Solana-specific risks
    riskScore += 0.1; // Base risk for newer ecosystem
    riskFactors.push('Solana ecosystem - higher volatility');
  } else {
    // EVM chain risks
    if (!data.verification.isVerified) {
      riskScore += 0.3;
      riskFactors.push('Contract source code not verified');
    }

    if (data.holderData.concentration?.top10 > 80) {
      riskScore += 0.4;
      riskFactors.push(`Very high concentration: Top 10 holders own ${data.holderData.concentration.top10.toFixed(1)}%`);
    }
  }

  // AI research factors
  if (data.aiResearch.warnings?.length > 0) {
    const highSeverityWarnings = data.aiResearch.warnings.filter(w => w.severity === 'high').length;
    riskScore += highSeverityWarnings * 0.2;
    riskFactors.push(...data.aiResearch.warnings.map(w => w.message));
  }

  if (data.aiResearch.sentiment?.sentiment === 'negative') {
    riskScore += 0.15;
    riskFactors.push('Negative market sentiment detected');
  }

  // Determine risk level
  let riskLevel = 'low';
  if (riskScore >= 0.8) riskLevel = 'critical';
  else if (riskScore >= 0.6) riskLevel = 'high';
  else if (riskScore >= 0.4) riskLevel = 'medium';

  return {
    riskScore: Math.min(riskScore, 1),
    riskLevel,
    riskFactors,
    recommendation: getEnhancedRecommendation(riskLevel, riskScore, data.aiResearch)
  };
}

function getEnhancedRecommendation(riskLevel, riskScore, aiResearch) {
  let base = '';
  switch (riskLevel) {
    case 'critical':
      base = '🚨 EXTREME CAUTION: Multiple high-risk factors detected.';
      break;
    case 'high':
      base = '⚠️ HIGH RISK: Significant concerns identified.';
      break;
    case 'medium':
      base = '🟡 MODERATE RISK: Some concerns present.';
      break;
    default:
      base = '✅ LOWER RISK: Fewer red flags detected.';
  }
  
  if (aiResearch.aiSummary) {
    base += ` AI Analysis: ${aiResearch.aiSummary.split('Recommendation: ')[1] || 'Conduct thorough research.'}`;
  }
  
  return base;
}

// Placeholder functions for EVM chains (same as before)
async function getTokenInfo(address, network) {
  // Implementation from previous version
  return {
    name: 'Example Token',
    symbol: 'EXT',
    decimals: 18,
    totalSupply: '1000000000000000000000000',
    owner: null
  };
}

async function getContractVerification(address, network) {
  try {
    const apiKey = network === 'ethereum' ? ETHERSCAN_API_KEY : BSCSCAN_API_KEY;
    const baseUrl = network === 'ethereum' ? 'https://api.etherscan.io/api' : 'https://api.bscscan.com/api';
    
    const url = `${baseUrl}?module=contract&action=getsourcecode&address=${address}&apikey=${apiKey}`;
    const response = await makeRequest(url);
    
    if (response.status !== '1') {
      throw new Error('Failed to fetch contract data');
    }

    const result = response.result[0];
    const isVerified = result.SourceCode !== '';
    
    return {
      isVerified,
      contractName: result.ContractName || 'Unknown',
      compilerVersion: result.CompilerVersion || 'Unknown',
      proxy: result.Proxy === '1'
    };
  } catch (error) {
    return {
      isVerified: false,
      error: 'Unable to check verification status'
    };
  }
}

async function getHolderData(address, network) {
  return {
    totalHolders: Math.floor(100 + Math.random() * 5000),
    concentration: {
      top10: 60 + Math.random() * 25,
      top50: 80 + Math.random() * 15,
      top100: 90 + Math.random() * 8
    }
  };
}

// Enhanced HTML template with Solana support and AI features
const htmlTemplate = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DumpDetector - Multi-Chain AI Token Analyzer</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
            color: #ffffff;
            min-height: 100vh;
            padding: 20px;
        }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { text-align: center; margin-bottom: 40px; }
        .header h1 { font-size: 3rem; margin-bottom: 10px; color: #00ff88; text-shadow: 0 0 20px rgba(0, 255, 136, 0.3); }
        .header p { font-size: 1.2rem; color: #cccccc; }
        
        .features-banner {
            background: linear-gradient(45deg, #00ff88, #0088ff);
            color: #000;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 30px;
            font-weight: bold;
        }
        
        .analyzer-form { 
            background: rgba(255, 255, 255, 0.05); 
            border: 1px solid #333; 
            border-radius: 15px; 
            padding: 30px; 
            margin-bottom: 30px;
            text-align: center;
            backdrop-filter: blur(10px);
        }
        .form-group { margin-bottom: 20px; }
        .form-group label { display: block; margin-bottom: 10px; font-weight: bold; color: #00ff88; }
        .form-group input, .form-group select { 
            width: 100%; 
            max-width: 600px;
            padding: 15px; 
            border: 1px solid #333; 
            border-radius: 8px; 
            background: #1a1a1a; 
            color: #ffffff;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #00ff88;
            box-shadow: 0 0 10px rgba(0, 255, 136, 0.2);
        }
        .analyze-btn { 
            background: linear-gradient(45deg, #00ff88, #00cc6a);
            color: #000; 
            border: none; 
            padding: 15px 30px; 
            border-radius: 8px; 
            cursor: pointer; 
            font-weight: bold; 
            font-size: 16px;
            margin-top: 10px;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .analyze-btn:hover { 
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 255, 136, 0.3);
        }
        .analyze-btn:disabled { 
            background: #666; 
            cursor: not-allowed; 
            transform: none;
            box-shadow: none;
        }
        
        .loading { 
            display: none; 
            text-align: center; 
            margin: 20px 0; 
            padding: 20px;
            background: rgba(0, 255, 136, 0.1);
            border-radius: 10px;
            border: 1px solid #00ff88;
        }
        .loading.show { display: block; }
        .loading h3 { color: #00ff88; margin-bottom: 10px; }
        
        .results { display: none; }
        .results.show { display: block; }
        
        .result-section { 
            background: rgba(255, 255, 255, 0.05); 
            border: 1px solid #333; 
            border-radius: 15px; 
            padding: 25px; 
            margin-bottom: 25px;
            backdrop-filter: blur(10px);
        }
        .result-section h3 { 
            color: #00ff88; 
            margin-bottom: 20px; 
            font-size: 1.4rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .risk-badge { 
            padding: 12px 24px; 
            border-radius: 25px; 
            font-weight: bold; 
            text-transform: uppercase;
            display: inline-block;
            margin-bottom: 15px;
            font-size: 1.1rem;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }
        .risk-low { background: linear-gradient(45deg, #00ff88, #00cc6a); color: #000; }
        .risk-medium { background: linear-gradient(45deg, #ffaa00, #ff8800); color: #000; }
        .risk-high { background: linear-gradient(45deg, #ff4444, #cc0000); color: #fff; }
        .risk-critical { background: linear-gradient(45deg, #ff0000, #990000); color: #fff; animation: pulse 2s infinite; }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        
        .info-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 20px; }
        .info-item { 
            background: #1a1a1a; 
            padding: 20px; 
            border-radius: 10px; 
            border: 1px solid #333;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .info-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }
        .info-label { color: #888; font-size: 0.9rem; margin-bottom: 8px; text-transform: uppercase; letter-spacing: 1px; }
        .info-value { color: #fff; font-weight: bold; font-size: 1.1rem; }
        
        .ai-summary {
            background: linear-gradient(45deg, rgba(0, 255, 136, 0.1), rgba(0, 136, 255, 0.1));
            border: 1px solid #00ff88;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            white-space: pre-line;
        }
        
        .network-solana { color: #9945ff; }
        .network-ethereum { color: #627eea; }
        .network-bsc { color: #f3ba2f; }
        
        .error { 
            background: rgba(255, 68, 68, 0.1); 
            border: 1px solid #ff4444; 
            border-radius: 10px; 
            padding: 25px; 
            color: #ff4444;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 DumpDetector</h1>
            <p>Multi-Chain AI Token Analyzer</p>
        </div>

        <div class="features-banner">
            🚀 NEW: Solana Support + AI-Powered Research | Multi-Chain Analysis | Real-Time Data
        </div>

        <div class="analyzer-form">
            <div class="form-group">
                <label for="tokenAddress">🔗 Token Contract Address</label>
                <input type="text" id="tokenAddress" placeholder="Enter Ethereum, BSC, or Solana token address..." />
            </div>
            <div class="form-group">
                <label for="network">🌐 Blockchain Network</label>
                <select id="network">
                    <option value="ethereum">Ethereum Mainnet</option>
                    <option value="bsc">Binance Smart Chain</option>
                    <option value="solana">Solana Mainnet</option>
                </select>
            </div>
            <button class="analyze-btn" onclick="analyzeToken()">🤖 AI-Powered Analysis</button>
            <div style="margin-top: 15px; font-size: 0.9rem; color: #888;">
                ✅ Multi-chain support • ✅ AI research • ✅ Real-time data • ✅ Free APIs
            </div>
        </div>

        <div class="loading">
            <h3>🤖 AI Analysis in Progress...</h3>
            <p>Fetching blockchain data • Analyzing contract • Researching online • Generating AI report...</p>
        </div>

        <div class="results" id="results"></div>
    </div>

    <script>
        async function analyzeToken() {
            const address = document.getElementById('tokenAddress').value.trim();
            const network = document.getElementById('network').value;
            
            if (!address) {
                alert('Please enter a token contract address');
                return;
            }
            
            // Validate address format based on network
            if (network === 'solana') {
                if (!/^[1-9A-HJ-NP-Za-km-z]{32,44}$/.test(address)) {
                    alert('Please enter a valid Solana token address (base58 format)');
                    return;
                }
            } else {
                if (!/^0x[a-fA-F0-9]{40}$/.test(address)) {
                    alert('Please enter a valid contract address (42 characters starting with 0x)');
                    return;
                }
            }
            
            // Show loading
            document.querySelector('.loading').classList.add('show');
            document.querySelector('.results').classList.remove('show');
            document.querySelector('.analyze-btn').disabled = true;
            
            try {
                const response = await fetch('/api/analyze', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ address, network })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    displayResults(result.data);
                } else {
                    displayError(result.error);
                }
            } catch (error) {
                displayError('Network error: ' + error.message);
            } finally {
                document.querySelector('.loading').classList.remove('show');
                document.querySelector('.analyze-btn').disabled = false;
            }
        }
        
        function displayResults(data) {
            const resultsDiv = document.getElementById('results');
            
            resultsDiv.innerHTML = \`
                <!-- Risk Assessment -->
                <div class="result-section">
                    <h3>🚨 AI Risk Assessment</h3>
                    <div class="risk-badge risk-\${data.riskAnalysis.riskLevel}">
                        \${data.riskAnalysis.riskLevel.toUpperCase()} RISK (\${(data.riskAnalysis.riskScore * 100).toFixed(1)}%)
                    </div>
                    <p style="font-size: 1.1rem; margin-bottom: 15px;"><strong>AI Recommendation:</strong> \${data.riskAnalysis.recommendation}</p>
                    \${data.riskAnalysis.riskFactors.length > 0 ? \`
                        <div style="margin-top: 20px;">
                            <strong>⚠️ Risk Factors Identified:</strong>
                            <ul style="margin-left: 20px; margin-top: 10px; line-height: 1.6;">
                                \${data.riskAnalysis.riskFactors.map(factor => \`<li style="margin-bottom: 5px;">\${factor}</li>\`).join('')}
                            </ul>
                        </div>
                    \` : '<div style="color: #00ff88; margin-top: 15px;">✅ No major risk factors detected</div>'}
                    
                    \${data.aiResearch.aiSummary ? \`
                        <div class="ai-summary">
                            <strong>🤖 AI Analysis Summary:</strong><br><br>
                            \${data.aiResearch.aiSummary}
                        </div>
                    \` : ''}
                </div>
                
                <!-- Basic Information -->
                <div class="result-section">
                    <h3>📋 Token Information</h3>
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">Token Name</div>
                            <div class="info-value">\${data.basic.name}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Symbol</div>
                            <div class="info-value">\${data.basic.symbol}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Network</div>
                            <div class="info-value network-\${data.basic.network}">\${data.basic.network.toUpperCase()}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Decimals</div>
                            <div class="info-value">\${data.basic.decimals}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Total Supply</div>
                            <div class="info-value">\${formatNumber(data.basic.totalSupply)}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Address</div>
                            <div class="info-value" style="font-family: monospace; font-size: 0.9rem;">\${data.basic.address}</div>
                        </div>
                    </div>
                </div>
                
                \${data.basic.network !== 'solana' ? \`
                    <!-- Verification Status -->
                    <div class="result-section">
                        <h3>✅ Contract Verification</h3>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">Verification Status</div>
                                <div class="info-value \${data.verification.isVerified ? 'verified' : 'unverified'}">
                                    \${data.verification.isVerified ? '✅ Verified' : '❌ Not Verified'}
                                </div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">Contract Name</div>
                                <div class="info-value">\${data.verification.contractName || 'Unknown'}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">Proxy Contract</div>
                                <div class="info-value">\${data.verification.proxy ? '⚠️ Yes (Upgradeable)' : '✅ No'}</div>
                            </div>
                        </div>
                    </div>
                \` : ''}
                
                <!-- AI Research Results -->
                <div class="result-section">
                    <h3>🤖 AI Research Results</h3>
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">Market Sentiment</div>
                            <div class="info-value">\${data.aiResearch.sentiment?.sentiment?.toUpperCase() || 'UNKNOWN'}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Social Media Mentions</div>
                            <div class="info-value">\${data.aiResearch.socialMedia?.twitter?.mentions || 'N/A'}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">News Articles</div>
                            <div class="info-value">\${data.aiResearch.news?.length || 0} found</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">AI Warnings</div>
                            <div class="info-value">\${data.aiResearch.warnings?.length || 0} detected</div>
                        </div>
                    </div>
                    
                    \${data.aiResearch.warnings?.length > 0 ? \`
                        <div style="margin-top: 20px;">
                            <strong>⚠️ AI-Detected Warnings:</strong>
                            <ul style="margin-left: 20px; margin-top: 10px;">
                                \${data.aiResearch.warnings.map(warning => \`
                                    <li style="margin-bottom: 5px;">
                                        <span style="color: \${warning.severity === 'high' ? '#ff4444' : '#ffaa00'};">
                                            [\${warning.severity.toUpperCase()}]
                                        </span> 
                                        \${warning.message}
                                    </li>
                                \`).join('')}
                            </ul>
                        </div>
                    \` : ''}
                </div>
            \`;
            
            document.querySelector('.results').classList.add('show');
            document.querySelector('.results').scrollIntoView({ behavior: 'smooth' });
        }
        
        function displayError(error) {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = \`
                <div class="error">
                    <h3>❌ Analysis Failed</h3>
                    <p style="margin-top: 15px; font-size: 1.1rem;">\${error}</p>
                </div>
            \`;
            document.querySelector('.results').classList.add('show');
        }
        
        function formatNumber(num) {
            if (!num) return '0';
            if (typeof num === 'string' && num.startsWith('0x')) {
                const n = parseInt(num, 16);
                return n.toLocaleString();
            }
            const n = parseFloat(num);
            if (n >= 1e9) return (n / 1e9).toFixed(2) + 'B';
            if (n >= 1e6) return (n / 1e6).toFixed(2) + 'M';
            if (n >= 1e3) return (n / 1e3).toFixed(2) + 'K';
            return n.toLocaleString();
        }
        
        // Allow Enter key to trigger analysis
        document.getElementById('tokenAddress').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                analyzeToken();
            }
        });
        
        // Network change handler
        document.getElementById('network').addEventListener('change', function(e) {
            const placeholder = e.target.value === 'solana' 
                ? 'Enter Solana token address (base58 format)...'
                : 'Enter contract address (0x...)...';
            document.getElementById('tokenAddress').placeholder = placeholder;
        });
    </script>
</body>
</html>
`;

// HTTP Server
const server = createServer(async (req, res) => {
  const url = req.url;
  const method = req.method;
  
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  if (url === '/' || url === '/index.html') {
    res.writeHead(200, { 'Content-Type': 'text/html' });
    res.end(htmlTemplate);
  } else if (url === '/api/analyze' && method === 'POST') {
    let body = '';
    req.on('data', chunk => body += chunk.toString());
    req.on('end', async () => {
      try {
        const { address, network } = JSON.parse(body);
        const result = await analyzeToken(address, network);
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify(result));
      } catch (error) {
        res.writeHead(400, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ success: false, error: error.message }));
      }
    });
  } else {
    res.writeHead(404, { 'Content-Type': 'text/plain' });
    res.end('Not Found');
  }
});

server.listen(PORT, () => {
  console.log('🚀 DumpDetector Enhanced Multi-Chain AI Analyzer Started!');
  console.log('========================================================');
  console.log(`🌐 Web Interface: http://localhost:${PORT}`);
  console.log('========================================================');
  console.log('🔍 Enhanced Features:');
  console.log('  ✅ Multi-chain support (Ethereum, BSC, Solana)');
  console.log('  ✅ AI-powered token research');
  console.log('  ✅ Sentiment analysis');
  console.log('  ✅ Social media monitoring');
  console.log('  ✅ News and article search');
  console.log('  ✅ Automated warning detection');
  console.log('  ✅ Enhanced risk scoring');
  console.log('========================================================');
  console.log('🔑 Free APIs Used:');
  console.log('  ✅ Solana RPC (free)');
  console.log('  ✅ Jupiter API (free)');
  console.log('  ✅ Solscan API (free tier)');
  console.log('  ✅ Your existing Etherscan/BSCScan keys');
  console.log('========================================================');
  console.log('💡 Test addresses:');
  console.log('   Ethereum USDT: ******************************************');
  console.log('   Solana USDC: EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v');
  console.log('   BSC BUSD: ******************************************');
});

export default server;
