// Type definitions for DumpDetector frontend

export interface Token {
  _id: string;
  address: string;
  name: string;
  symbol: string;
  network: string;
  decimals: number;
  totalSupply: string;
  
  contract: {
    creator: string;
    creationTx: string;
    creationBlock: number;
    isProxy: boolean;
    implementation?: string;
    verified: boolean;
    sourceCode?: string;
  };
  
  liquidity: {
    totalLiquidity: number;
    locked: boolean;
    lockDuration: number;
    lockContract?: string;
    pairs: Array<{
      dex: string;
      pairAddress: string;
      baseToken: string;
      quoteToken: string;
      liquidity: number;
      volume24h: number;
    }>;
  };
  
  ownership: {
    ownerAddress?: string;
    ownershipRenounced: boolean;
    topHolders: Array<{
      address: string;
      balance: string;
      percentage: number;
    }>;
    concentration: {
      top10Percentage: number;
      top50Percentage: number;
      top100Percentage: number;
    };
  };
  
  analysis: {
    isScam: boolean;
    isRugpull: boolean;
    isHoneypot: boolean;
    hasHiddenMint: boolean;
    hasBlacklist: boolean;
    hasPausable: boolean;
    hasProxyUpgrade: boolean;
    hasHighFees: boolean;
    maxTxAmount?: string;
    maxWalletAmount?: string;
    buyTax: number;
    sellTax: number;
    lastAnalyzed: string;
  };
  
  social: {
    twitter?: {
      followers: number;
      verified: boolean;
      createdAt: string;
      suspicious: boolean;
    };
    telegram?: {
      members: number;
      botPercentage: number;
      suspicious: boolean;
    };
    discord?: {
      members: number;
      suspicious: boolean;
    };
    website?: string;
    whitepaper?: string;
  };
  
  market: {
    price: number;
    priceChange24h: number;
    volume24h: number;
    marketCap: number;
    holders: number;
    transactions24h: number;
  };
  
  riskScore: number;
  riskFactors: Array<{
    factor: string;
    weight: number;
    score: number;
    description: string;
  }>;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  
  tags: string[];
  status: 'active' | 'inactive' | 'flagged' | 'verified';
  
  createdAt: string;
  updatedAt: string;
}

export interface Alert {
  _id: string;
  tokenAddress: string;
  tokenName: string;
  tokenSymbol: string;
  network: string;
  
  alertType: 'scam_detected' | 'rugpull_risk' | 'honeypot_detected' | 'high_risk' | 'suspicious_activity';
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  riskScore: number;
  
  message: string;
  
  details: {
    triggers: string[];
    analysis: any;
    recommendations: string[];
  };
  
  channels: {
    telegram: {
      sent: boolean;
      sentAt?: string;
      messageId?: string;
      chatId?: string;
    };
    discord: {
      sent: boolean;
      sentAt?: string;
      messageId?: string;
    };
    webhook: {
      sent: boolean;
      sentAt?: string;
      response?: string;
    };
  };
  
  sent: boolean;
  createdAt: string;
}

export interface Analysis {
  _id: string;
  tokenAddress: string;
  analysisType: 'contract' | 'social' | 'market' | 'liquidity' | 'ownership';
  results: any;
  score: number;
  confidence: number;
  
  metadata: {
    analyzer: string;
    version: string;
    duration: number;
    dataSource: string;
  };
  
  createdAt: string;
}

export interface SocialSignal {
  _id: string;
  tokenAddress: string;
  platform: 'twitter' | 'telegram' | 'discord' | 'reddit';
  signalType: 'mention' | 'sentiment' | 'volume' | 'bot_activity' | 'promotion';
  
  content: {
    text: string;
    author: string;
    authorId: string;
    url: string;
    engagement: {
      likes: number;
      shares: number;
      comments: number;
      views: number;
    };
  };
  
  sentiment: 'positive' | 'negative' | 'neutral';
  sentimentScore: number;
  suspicious: boolean;
  suspiciousReasons: string[];
  
  metadata: {
    language: string;
    location?: string;
    timestamp: string;
    source: string;
  };
  
  createdAt: string;
}

export interface User {
  _id: string;
  telegramId?: string;
  username?: string;
  email?: string;
  
  preferences: {
    notifications: {
      telegram: boolean;
      email: boolean;
      webhook?: string;
    };
    riskThreshold: 'low' | 'medium' | 'high';
    networks: string[];
    alertTypes: string[];
  };
  
  subscription: {
    plan: 'free' | 'premium' | 'enterprise';
    expiresAt?: string;
    features: string[];
  };
  
  apiKey?: string;
  
  usage: {
    apiCalls: number;
    lastApiCall?: string;
    alertsSent: number;
  };
  
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface SystemStats {
  tokensMonitored: number;
  alertsSent24h: number;
  detectionRate: number;
  networks: Array<{
    name: string;
    status: 'online' | 'offline' | 'warning';
  }>;
  uptime: number;
  lastUpdate: string;
}

export interface DetectionStats {
  totalScanned: number;
  scamsDetected: number;
  rugpullsPrevented: number;
  usersProtected: number;
  accuracy: number;
  responseTime: number;
}

export interface RiskThresholds {
  high: number;
  medium: number;
  low: number;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export interface SocketEvents {
  token_detected: Token;
  risk_alert: Alert;
  analysis_complete: Analysis;
  system_status: SystemStats;
}
